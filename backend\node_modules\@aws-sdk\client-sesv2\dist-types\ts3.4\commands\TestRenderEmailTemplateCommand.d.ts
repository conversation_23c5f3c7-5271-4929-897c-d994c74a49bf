import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  TestRenderEmailTemplateRequest,
  TestRenderEmailTemplateResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface TestRenderEmailTemplateCommandInput
  extends TestRenderEmailTemplateRequest {}
export interface TestRenderEmailTemplateCommandOutput
  extends TestRenderEmailTemplateResponse,
    __MetadataBearer {}
declare const TestRenderEmailTemplateCommand_base: {
  new (
    input: TestRenderEmailTemplateCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    TestRenderEmailTemplateCommandInput,
    TestRenderEmailTemplateCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: TestRenderEmailTemplateCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    TestRenderEmailTemplateCommandInput,
    TestRenderEmailTemplateCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class TestRenderEmailTemplateCommand extends TestRenderEmailTemplateCommand_base {
  protected static __types: {
    api: {
      input: TestRenderEmailTemplateRequest;
      output: TestRenderEmailTemplateResponse;
    };
    sdk: {
      input: TestRenderEmailTemplateCommandInput;
      output: TestRenderEmailTemplateCommandOutput;
    };
  };
}
