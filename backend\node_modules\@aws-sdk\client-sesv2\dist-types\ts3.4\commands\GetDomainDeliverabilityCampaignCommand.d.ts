import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetDomainDeliverabilityCampaignRequest,
  GetDomainDeliverabilityCampaignResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetDomainDeliverabilityCampaignCommandInput
  extends GetDomainDeliverabilityCampaignRequest {}
export interface GetDomainDeliverabilityCampaignCommandOutput
  extends GetDomainDeliverabilityCampaignResponse,
    __MetadataBearer {}
declare const GetDomainDeliverabilityCampaignCommand_base: {
  new (
    input: GetDomainDeliverabilityCampaignCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetDomainDeliverabilityCampaignCommandInput,
    GetDomainDeliverabilityCampaignCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetDomainDeliverabilityCampaignCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetDomainDeliverabilityCampaignCommandInput,
    GetDomainDeliverabilityCampaignCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetDomainDeliverabilityCampaignCommand extends GetDomainDeliverabilityCampaignCommand_base {
  protected static __types: {
    api: {
      input: GetDomainDeliverabilityCampaignRequest;
      output: GetDomainDeliverabilityCampaignResponse;
    };
    sdk: {
      input: GetDomainDeliverabilityCampaignCommandInput;
      output: GetDomainDeliverabilityCampaignCommandOutput;
    };
  };
}
