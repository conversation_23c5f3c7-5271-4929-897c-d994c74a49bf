import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutSuppressedDestinationRequest,
  PutSuppressedDestinationResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutSuppressedDestinationCommandInput
  extends PutSuppressedDestinationRequest {}
export interface PutSuppressedDestinationCommandOutput
  extends PutSuppressedDestinationResponse,
    __MetadataBearer {}
declare const PutSuppressedDestinationCommand_base: {
  new (
    input: PutSuppressedDestinationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutSuppressedDestinationCommandInput,
    PutSuppressedDestinationCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutSuppressedDestinationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutSuppressedDestinationCommandInput,
    PutSuppressedDestinationCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutSuppressedDestinationCommand extends PutSuppressedDestinationCommand_base {
  protected static __types: {
    api: {
      input: PutSuppressedDestinationRequest;
      output: {};
    };
    sdk: {
      input: PutSuppressedDestinationCommandInput;
      output: PutSuppressedDestinationCommandOutput;
    };
  };
}
