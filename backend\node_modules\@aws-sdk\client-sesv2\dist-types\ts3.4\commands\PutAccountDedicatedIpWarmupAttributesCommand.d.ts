import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutAccountDedicatedIpWarmupAttributesRequest,
  PutAccountDedicatedIpWarmupAttributesResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutAccountDedicatedIpWarmupAttributesCommandInput
  extends PutAccountDedicatedIpWarmupAttributesRequest {}
export interface PutAccountDedicatedIpWarmupAttributesCommandOutput
  extends PutAccountDedicatedIpWarmupAttributesResponse,
    __MetadataBearer {}
declare const PutAccountDedicatedIpWarmupAttributesCommand_base: {
  new (
    input: PutAccountDedicatedIpWarmupAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutAccountDedicatedIpWarmupAttributesCommandInput,
    PutAccountDedicatedIpWarmupAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [PutAccountDedicatedIpWarmupAttributesCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    PutAccountDedicatedIpWarmupAttributesCommandInput,
    PutAccountDedicatedIpWarmupAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutAccountDedicatedIpWarmupAttributesCommand extends PutAccountDedicatedIpWarmupAttributesCommand_base {
  protected static __types: {
    api: {
      input: PutAccountDedicatedIpWarmupAttributesRequest;
      output: {};
    };
    sdk: {
      input: PutAccountDedicatedIpWarmupAttributesCommandInput;
      output: PutAccountDedicatedIpWarmupAttributesCommandOutput;
    };
  };
}
