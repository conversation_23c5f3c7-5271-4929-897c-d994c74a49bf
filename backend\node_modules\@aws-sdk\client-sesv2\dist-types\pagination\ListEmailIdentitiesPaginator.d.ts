import { Paginator } from "@smithy/types";
import { ListEmailIdentitiesCommandInput, ListEmailIdentitiesCommandOutput } from "../commands/ListEmailIdentitiesCommand";
import { SESv2PaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListEmailIdentities: (config: SESv2PaginationConfiguration, input: ListEmailIdentitiesCommandInput, ...rest: any[]) => Paginator<ListEmailIdentitiesCommandOutput>;
