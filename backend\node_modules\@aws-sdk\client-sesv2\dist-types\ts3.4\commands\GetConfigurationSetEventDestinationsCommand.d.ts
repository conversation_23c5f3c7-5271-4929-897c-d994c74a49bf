import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetConfigurationSetEventDestinationsRequest,
  GetConfigurationSetEventDestinationsResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetConfigurationSetEventDestinationsCommandInput
  extends GetConfigurationSetEventDestinationsRequest {}
export interface GetConfigurationSetEventDestinationsCommandOutput
  extends GetConfigurationSetEventDestinationsResponse,
    __MetadataBearer {}
declare const GetConfigurationSetEventDestinationsCommand_base: {
  new (
    input: GetConfigurationSetEventDestinationsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetConfigurationSetEventDestinationsCommandInput,
    GetConfigurationSetEventDestinationsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetConfigurationSetEventDestinationsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetConfigurationSetEventDestinationsCommandInput,
    GetConfigurationSetEventDestinationsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetConfigurationSetEventDestinationsCommand extends GetConfigurationSetEventDestinationsCommand_base {
  protected static __types: {
    api: {
      input: GetConfigurationSetEventDestinationsRequest;
      output: GetConfigurationSetEventDestinationsResponse;
    };
    sdk: {
      input: GetConfigurationSetEventDestinationsCommandInput;
      output: GetConfigurationSetEventDestinationsCommandOutput;
    };
  };
}
