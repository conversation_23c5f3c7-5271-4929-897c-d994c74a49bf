import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  GetMultiRegionEndpointRequest,
  GetMultiRegionEndpointResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetMultiRegionEndpointCommandInput
  extends GetMultiRegionEndpointRequest {}
export interface GetMultiRegionEndpointCommandOutput
  extends GetMultiRegionEndpointResponse,
    __MetadataBearer {}
declare const GetMultiRegionEndpointCommand_base: {
  new (
    input: GetMultiRegionEndpointCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetMultiRegionEndpointCommandInput,
    GetMultiRegionEndpointCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetMultiRegionEndpointCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetMultiRegionEndpointCommandInput,
    GetMultiRegionEndpointCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetMultiRegionEndpointCommand extends GetMultiRegionEndpointCommand_base {
  protected static __types: {
    api: {
      input: GetMultiRegionEndpointRequest;
      output: GetMultiRegionEndpointResponse;
    };
    sdk: {
      input: GetMultiRegionEndpointCommandInput;
      output: GetMultiRegionEndpointCommandOutput;
    };
  };
}
