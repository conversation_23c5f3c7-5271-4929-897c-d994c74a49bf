import { Paginator } from "@smithy/types";
import { ListContactListsCommandInput, ListContactListsCommandOutput } from "../commands/ListContactListsCommand";
import { SESv2PaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListContactLists: (config: SESv2PaginationConfiguration, input: ListContactListsCommandInput, ...rest: any[]) => Paginator<ListContactListsCommandOutput>;
