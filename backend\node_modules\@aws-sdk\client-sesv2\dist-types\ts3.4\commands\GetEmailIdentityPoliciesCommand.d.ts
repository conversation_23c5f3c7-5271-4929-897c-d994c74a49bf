import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetEmailIdentityPoliciesRequest,
  GetEmailIdentityPoliciesResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetEmailIdentityPoliciesCommandInput
  extends GetEmailIdentityPoliciesRequest {}
export interface GetEmailIdentityPoliciesCommandOutput
  extends GetEmailIdentityPoliciesResponse,
    __MetadataBearer {}
declare const GetEmailIdentityPoliciesCommand_base: {
  new (
    input: GetEmailIdentityPoliciesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetEmailIdentityPoliciesCommandInput,
    GetEmailIdentityPoliciesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetEmailIdentityPoliciesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetEmailIdentityPoliciesCommandInput,
    GetEmailIdentityPoliciesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetEmailIdentityPoliciesCommand extends GetEmailIdentityPoliciesCommand_base {
  protected static __types: {
    api: {
      input: GetEmailIdentityPoliciesRequest;
      output: GetEmailIdentityPoliciesResponse;
    };
    sdk: {
      input: GetEmailIdentityPoliciesCommandInput;
      output: GetEmailIdentityPoliciesCommandOutput;
    };
  };
}
