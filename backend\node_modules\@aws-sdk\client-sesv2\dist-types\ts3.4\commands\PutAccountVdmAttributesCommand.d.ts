import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutAccountVdmAttributesRequest,
  PutAccountVdmAttributesResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutAccountVdmAttributesCommandInput
  extends PutAccountVdmAttributesRequest {}
export interface PutAccountVdmAttributesCommandOutput
  extends PutAccountVdmAttributesResponse,
    __MetadataBearer {}
declare const PutAccountVdmAttributesCommand_base: {
  new (
    input: PutAccountVdmAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutAccountVdmAttributesCommandInput,
    PutAccountVdmAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutAccountVdmAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutAccountVdmAttributesCommandInput,
    PutAccountVdmAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutAccountVdmAttributesCommand extends PutAccountVdmAttributesCommand_base {
  protected static __types: {
    api: {
      input: PutAccountVdmAttributesRequest;
      output: {};
    };
    sdk: {
      input: PutAccountVdmAttributesCommandInput;
      output: PutAccountVdmAttributesCommandOutput;
    };
  };
}
