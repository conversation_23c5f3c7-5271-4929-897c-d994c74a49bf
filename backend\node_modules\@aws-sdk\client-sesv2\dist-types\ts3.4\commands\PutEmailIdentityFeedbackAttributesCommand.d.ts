import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutEmailIdentityFeedbackAttributesRequest,
  PutEmailIdentityFeedbackAttributesResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutEmailIdentityFeedbackAttributesCommandInput
  extends PutEmailIdentityFeedbackAttributesRequest {}
export interface PutEmailIdentityFeedbackAttributesCommandOutput
  extends PutEmailIdentityFeedbackAttributesResponse,
    __MetadataBearer {}
declare const PutEmailIdentityFeedbackAttributesCommand_base: {
  new (
    input: PutEmailIdentityFeedbackAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutEmailIdentityFeedbackAttributesCommandInput,
    PutEmailIdentityFeedbackAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutEmailIdentityFeedbackAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutEmailIdentityFeedbackAttributesCommandInput,
    PutEmailIdentityFeedbackAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutEmailIdentityFeedbackAttributesCommand extends PutEmailIdentityFeedbackAttributesCommand_base {
  protected static __types: {
    api: {
      input: PutEmailIdentityFeedbackAttributesRequest;
      output: {};
    };
    sdk: {
      input: PutEmailIdentityFeedbackAttributesCommandInput;
      output: PutEmailIdentityFeedbackAttributesCommandOutput;
    };
  };
}
