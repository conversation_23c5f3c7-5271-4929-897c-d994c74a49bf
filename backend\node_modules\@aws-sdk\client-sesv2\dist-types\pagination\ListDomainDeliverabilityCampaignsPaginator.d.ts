import { Paginator } from "@smithy/types";
import { ListDomainDeliverabilityCampaignsCommandInput, ListDomainDeliverabilityCampaignsCommandOutput } from "../commands/ListDomainDeliverabilityCampaignsCommand";
import { SESv2PaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListDomainDeliverabilityCampaigns: (config: SESv2PaginationConfiguration, input: ListDomainDeliverabilityCampaignsCommandInput, ...rest: any[]) => Paginator<ListDomainDeliverabilityCampaignsCommandOutput>;
