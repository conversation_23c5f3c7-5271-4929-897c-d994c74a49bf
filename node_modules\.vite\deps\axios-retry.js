import {
  __commonJS,
  __toESM
} from "./chunk-DC5AMYBS.js";

// node_modules/is-retry-allowed/index.js
var require_is_retry_allowed = __commonJS({
  "node_modules/is-retry-allowed/index.js"(exports, module) {
    "use strict";
    var denyList = /* @__PURE__ */ new Set([
      "ENOTFOUND",
      "ENETUNREACH",
      // SSL errors from https://github.com/nodejs/node/blob/fc8e3e2cdc521978351de257030db0076d79e0ab/src/crypto/crypto_common.cc#L301-L328
      "UNABLE_TO_GET_ISSUER_CERT",
      "UNABLE_TO_GET_CRL",
      "UNABLE_TO_DECRYPT_CERT_SIGNATURE",
      "UNABLE_TO_DECRYPT_CRL_SIGNATURE",
      "UNABLE_TO_DECODE_ISSUER_PUBLIC_KEY",
      "CERT_SIGNATURE_FAILURE",
      "CRL_SIGNATURE_FAILURE",
      "CERT_NOT_YET_VALID",
      "CERT_HAS_EXPIRED",
      "CRL_NOT_YET_VALID",
      "CRL_HAS_EXPIRED",
      "ERROR_IN_CERT_NOT_BEFORE_FIELD",
      "ERROR_IN_CERT_NOT_AFTER_FIELD",
      "ERROR_IN_CRL_LAST_UPDATE_FIELD",
      "ERROR_IN_CRL_NEXT_UPDATE_FIELD",
      "OUT_OF_MEM",
      "DEPTH_ZERO_SELF_SIGNED_CERT",
      "SELF_SIGNED_CERT_IN_CHAIN",
      "UNABLE_TO_GET_ISSUER_CERT_LOCALLY",
      "UNABLE_TO_VERIFY_LEAF_SIGNATURE",
      "CERT_CHAIN_TOO_LONG",
      "CERT_REVOKED",
      "INVALID_CA",
      "PATH_LENGTH_EXCEEDED",
      "INVALID_PURPOSE",
      "CERT_UNTRUSTED",
      "CERT_REJECTED",
      "HOSTNAME_MISMATCH"
    ]);
    module.exports = (error) => !denyList.has(error && error.code);
  }
});

// node_modules/axios-retry/dist/esm/index.js
var import_is_retry_allowed = __toESM(require_is_retry_allowed());
var namespace = "axios-retry";
function isNetworkError(error) {
  const CODE_EXCLUDE_LIST = ["ERR_CANCELED", "ECONNABORTED"];
  if (error.response) {
    return false;
  }
  if (!error.code) {
    return false;
  }
  if (CODE_EXCLUDE_LIST.includes(error.code)) {
    return false;
  }
  return (0, import_is_retry_allowed.default)(error);
}
var SAFE_HTTP_METHODS = ["get", "head", "options"];
var IDEMPOTENT_HTTP_METHODS = SAFE_HTTP_METHODS.concat(["put", "delete"]);
function isRetryableError(error) {
  return error.code !== "ECONNABORTED" && (!error.response || error.response.status === 429 || error.response.status >= 500 && error.response.status <= 599);
}
function isSafeRequestError(error) {
  var _a;
  if (!((_a = error.config) == null ? void 0 : _a.method)) {
    return false;
  }
  return isRetryableError(error) && SAFE_HTTP_METHODS.indexOf(error.config.method) !== -1;
}
function isIdempotentRequestError(error) {
  var _a;
  if (!((_a = error.config) == null ? void 0 : _a.method)) {
    return false;
  }
  return isRetryableError(error) && IDEMPOTENT_HTTP_METHODS.indexOf(error.config.method) !== -1;
}
function isNetworkOrIdempotentRequestError(error) {
  return isNetworkError(error) || isIdempotentRequestError(error);
}
function retryAfter(error = void 0) {
  var _a;
  const retryAfterHeader = (_a = error == null ? void 0 : error.response) == null ? void 0 : _a.headers["retry-after"];
  if (!retryAfterHeader) {
    return 0;
  }
  let retryAfterMs = (Number(retryAfterHeader) || 0) * 1e3;
  if (retryAfterMs === 0) {
    retryAfterMs = (new Date(retryAfterHeader).valueOf() || 0) - Date.now();
  }
  return Math.max(0, retryAfterMs);
}
function noDelay(_retryNumber = 0, error = void 0) {
  return Math.max(0, retryAfter(error));
}
function exponentialDelay(retryNumber = 0, error = void 0, delayFactor = 100) {
  const calculatedDelay = 2 ** retryNumber * delayFactor;
  const delay = Math.max(calculatedDelay, retryAfter(error));
  const randomSum = delay * 0.2 * Math.random();
  return delay + randomSum;
}
function linearDelay(delayFactor = 100) {
  return (retryNumber = 0, error = void 0) => {
    const delay = retryNumber * delayFactor;
    return Math.max(delay, retryAfter(error));
  };
}
var DEFAULT_OPTIONS = {
  retries: 3,
  retryCondition: isNetworkOrIdempotentRequestError,
  retryDelay: noDelay,
  shouldResetTimeout: false,
  onRetry: () => {
  },
  onMaxRetryTimesExceeded: () => {
  },
  validateResponse: null
};
function getRequestOptions(config, defaultOptions) {
  return { ...DEFAULT_OPTIONS, ...defaultOptions, ...config[namespace] };
}
function setCurrentState(config, defaultOptions, resetLastRequestTime = false) {
  const currentState = getRequestOptions(config, defaultOptions || {});
  currentState.retryCount = currentState.retryCount || 0;
  if (!currentState.lastRequestTime || resetLastRequestTime) {
    currentState.lastRequestTime = Date.now();
  }
  config[namespace] = currentState;
  return currentState;
}
function fixConfig(axiosInstance, config) {
  if (axiosInstance.defaults.agent === config.agent) {
    delete config.agent;
  }
  if (axiosInstance.defaults.httpAgent === config.httpAgent) {
    delete config.httpAgent;
  }
  if (axiosInstance.defaults.httpsAgent === config.httpsAgent) {
    delete config.httpsAgent;
  }
}
async function shouldRetry(currentState, error) {
  const { retries, retryCondition } = currentState;
  const shouldRetryOrPromise = (currentState.retryCount || 0) < retries && retryCondition(error);
  if (typeof shouldRetryOrPromise === "object") {
    try {
      const shouldRetryPromiseResult = await shouldRetryOrPromise;
      return shouldRetryPromiseResult !== false;
    } catch (_err) {
      return false;
    }
  }
  return shouldRetryOrPromise;
}
async function handleRetry(axiosInstance, currentState, error, config) {
  var _a;
  currentState.retryCount += 1;
  const { retryDelay, shouldResetTimeout, onRetry } = currentState;
  const delay = retryDelay(currentState.retryCount, error);
  fixConfig(axiosInstance, config);
  if (!shouldResetTimeout && config.timeout && currentState.lastRequestTime) {
    const lastRequestDuration = Date.now() - currentState.lastRequestTime;
    const timeout = config.timeout - lastRequestDuration - delay;
    if (timeout <= 0) {
      return Promise.reject(error);
    }
    config.timeout = timeout;
  }
  config.transformRequest = [(data) => data];
  await onRetry(currentState.retryCount, error, config);
  if ((_a = config.signal) == null ? void 0 : _a.aborted) {
    return Promise.resolve(axiosInstance(config));
  }
  return new Promise((resolve) => {
    var _a2;
    const abortListener = () => {
      clearTimeout(timeout);
      resolve(axiosInstance(config));
    };
    const timeout = setTimeout(() => {
      var _a3;
      resolve(axiosInstance(config));
      if ((_a3 = config.signal) == null ? void 0 : _a3.removeEventListener) {
        config.signal.removeEventListener("abort", abortListener);
      }
    }, delay);
    if ((_a2 = config.signal) == null ? void 0 : _a2.addEventListener) {
      config.signal.addEventListener("abort", abortListener, { once: true });
    }
  });
}
async function handleMaxRetryTimesExceeded(currentState, error) {
  if (currentState.retryCount >= currentState.retries)
    await currentState.onMaxRetryTimesExceeded(error, currentState.retryCount);
}
var axiosRetry = (axiosInstance, defaultOptions) => {
  const requestInterceptorId = axiosInstance.interceptors.request.use((config) => {
    var _a;
    setCurrentState(config, defaultOptions, true);
    if ((_a = config[namespace]) == null ? void 0 : _a.validateResponse) {
      config.validateStatus = () => false;
    }
    return config;
  });
  const responseInterceptorId = axiosInstance.interceptors.response.use(null, async (error) => {
    var _a;
    const { config } = error;
    if (!config) {
      return Promise.reject(error);
    }
    const currentState = setCurrentState(config, defaultOptions);
    if (error.response && ((_a = currentState.validateResponse) == null ? void 0 : _a.call(currentState, error.response))) {
      return error.response;
    }
    if (await shouldRetry(currentState, error)) {
      return handleRetry(axiosInstance, currentState, error, config);
    }
    await handleMaxRetryTimesExceeded(currentState, error);
    return Promise.reject(error);
  });
  return { requestInterceptorId, responseInterceptorId };
};
axiosRetry.isNetworkError = isNetworkError;
axiosRetry.isSafeRequestError = isSafeRequestError;
axiosRetry.isIdempotentRequestError = isIdempotentRequestError;
axiosRetry.isNetworkOrIdempotentRequestError = isNetworkOrIdempotentRequestError;
axiosRetry.exponentialDelay = exponentialDelay;
axiosRetry.linearDelay = linearDelay;
axiosRetry.isRetryableError = isRetryableError;
var esm_default = axiosRetry;
export {
  DEFAULT_OPTIONS,
  esm_default as default,
  exponentialDelay,
  isIdempotentRequestError,
  isNetworkError,
  isNetworkOrIdempotentRequestError,
  isRetryableError,
  isSafeRequestError,
  linearDelay,
  namespace,
  retryAfter
};
//# sourceMappingURL=axios-retry.js.map
