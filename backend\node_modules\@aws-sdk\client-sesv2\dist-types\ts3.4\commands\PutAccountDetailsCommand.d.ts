import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutAccountDetailsRequest,
  PutAccountDetailsResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutAccountDetailsCommandInput
  extends PutAccountDetailsRequest {}
export interface PutAccountDetailsCommandOutput
  extends PutAccountDetailsResponse,
    __MetadataBearer {}
declare const PutAccountDetailsCommand_base: {
  new (
    input: PutAccountDetailsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutAccountDetailsCommandInput,
    PutAccountDetailsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutAccountDetailsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutAccountDetailsCommandInput,
    PutAccountDetailsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutAccountDetailsCommand extends PutAccountDetailsCommand_base {
  protected static __types: {
    api: {
      input: PutAccountDetailsRequest;
      output: {};
    };
    sdk: {
      input: PutAccountDetailsCommandInput;
      output: PutAccountDetailsCommandOutput;
    };
  };
}
