import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateContactRequest,
  UpdateContactResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface UpdateContactCommandInput extends UpdateContactRequest {}
export interface UpdateContactCommandOutput
  extends UpdateContactResponse,
    __MetadataBearer {}
declare const UpdateContactCommand_base: {
  new (
    input: UpdateContactCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateContactCommandInput,
    UpdateContactCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateContactCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateContactCommandInput,
    UpdateContactCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateContactCommand extends UpdateContactCommand_base {
  protected static __types: {
    api: {
      input: UpdateContactRequest;
      output: {};
    };
    sdk: {
      input: UpdateContactCommandInput;
      output: UpdateContactCommandOutput;
    };
  };
}
