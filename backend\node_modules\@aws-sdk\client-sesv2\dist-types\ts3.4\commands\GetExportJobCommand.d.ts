import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { GetExportJobRequest, GetExportJobResponse } from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetExportJobCommandInput extends GetExportJobRequest {}
export interface GetExportJobCommandOutput
  extends GetExportJobResponse,
    __MetadataBearer {}
declare const GetExportJobCommand_base: {
  new (
    input: GetExportJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetExportJobCommandInput,
    GetExportJobCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetExportJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetExportJobCommandInput,
    GetExportJobCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetExportJobCommand extends GetExportJobCommand_base {
  protected static __types: {
    api: {
      input: GetExportJobRequest;
      output: GetExportJobResponse;
    };
    sdk: {
      input: GetExportJobCommandInput;
      output: GetExportJobCommandOutput;
    };
  };
}
