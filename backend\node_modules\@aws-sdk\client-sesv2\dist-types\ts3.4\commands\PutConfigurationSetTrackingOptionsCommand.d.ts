import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutConfigurationSetTrackingOptionsRequest,
  PutConfigurationSetTrackingOptionsResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutConfigurationSetTrackingOptionsCommandInput
  extends PutConfigurationSetTrackingOptionsRequest {}
export interface PutConfigurationSetTrackingOptionsCommandOutput
  extends PutConfigurationSetTrackingOptionsResponse,
    __MetadataBearer {}
declare const PutConfigurationSetTrackingOptionsCommand_base: {
  new (
    input: PutConfigurationSetTrackingOptionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutConfigurationSetTrackingOptionsCommandInput,
    PutConfigurationSetTrackingOptionsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutConfigurationSetTrackingOptionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutConfigurationSetTrackingOptionsCommandInput,
    PutConfigurationSetTrackingOptionsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutConfigurationSetTrackingOptionsCommand extends PutConfigurationSetTrackingOptionsCommand_base {
  protected static __types: {
    api: {
      input: PutConfigurationSetTrackingOptionsRequest;
      output: {};
    };
    sdk: {
      input: PutConfigurationSetTrackingOptionsCommandInput;
      output: PutConfigurationSetTrackingOptionsCommandOutput;
    };
  };
}
