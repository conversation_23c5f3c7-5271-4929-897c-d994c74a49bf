import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateContactListRequest,
  UpdateContactListResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface UpdateContactListCommandInput
  extends UpdateContactListRequest {}
export interface UpdateContactListCommandOutput
  extends UpdateContactListResponse,
    __MetadataBearer {}
declare const UpdateContactListCommand_base: {
  new (
    input: UpdateContactListCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateContactListCommandInput,
    UpdateContactListCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateContactListCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateContactListCommandInput,
    UpdateContactListCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateContactListCommand extends UpdateContactListCommand_base {
  protected static __types: {
    api: {
      input: UpdateContactListRequest;
      output: {};
    };
    sdk: {
      input: UpdateContactListCommandInput;
      output: UpdateContactListCommandOutput;
    };
  };
}
