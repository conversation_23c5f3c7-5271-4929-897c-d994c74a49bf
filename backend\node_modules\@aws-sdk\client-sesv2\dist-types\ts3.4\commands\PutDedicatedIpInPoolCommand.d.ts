import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  PutDedicatedIpInPoolRequest,
  PutDedicatedIpInPoolResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutDedicatedIpInPoolCommandInput
  extends PutDedicatedIpInPoolRequest {}
export interface PutDedicatedIpInPoolCommandOutput
  extends PutDedicatedIpInPoolResponse,
    __MetadataBearer {}
declare const PutDedicatedIpInPoolCommand_base: {
  new (
    input: PutDedicatedIpInPoolCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutDedicatedIpInPoolCommandInput,
    PutDedicatedIpInPoolCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutDedicatedIpInPoolCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutDedicatedIpInPoolCommandInput,
    PutDedicatedIpInPoolCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutDedicatedIpInPoolCommand extends PutDedicatedIpInPoolCommand_base {
  protected static __types: {
    api: {
      input: PutDedicatedIpInPoolRequest;
      output: {};
    };
    sdk: {
      input: PutDedicatedIpInPoolCommandInput;
      output: PutDedicatedIpInPoolCommandOutput;
    };
  };
}
