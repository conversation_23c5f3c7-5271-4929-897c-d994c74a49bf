import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetDeliverabilityTestReportRequest,
  GetDeliverabilityTestReportResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetDeliverabilityTestReportCommandInput
  extends GetDeliverabilityTestReportRequest {}
export interface GetDeliverabilityTestReportCommandOutput
  extends GetDeliverabilityTestReportResponse,
    __MetadataBearer {}
declare const GetDeliverabilityTestReportCommand_base: {
  new (
    input: GetDeliverabilityTestReportCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetDeliverabilityTestReportCommandInput,
    GetDeliverabilityTestReportCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetDeliverabilityTestReportCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetDeliverabilityTestReportCommandInput,
    GetDeliverabilityTestReportCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetDeliverabilityTestReportCommand extends GetDeliverabilityTestReportCommand_base {
  protected static __types: {
    api: {
      input: GetDeliverabilityTestReportRequest;
      output: GetDeliverabilityTestReportResponse;
    };
    sdk: {
      input: GetDeliverabilityTestReportCommandInput;
      output: GetDeliverabilityTestReportCommandOutput;
    };
  };
}
