import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  PutConfigurationSetArchivingOptionsRequest,
  PutConfigurationSetArchivingOptionsResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutConfigurationSetArchivingOptionsCommandInput
  extends PutConfigurationSetArchivingOptionsRequest {}
export interface PutConfigurationSetArchivingOptionsCommandOutput
  extends PutConfigurationSetArchivingOptionsResponse,
    __MetadataBearer {}
declare const PutConfigurationSetArchivingOptionsCommand_base: {
  new (
    input: PutConfigurationSetArchivingOptionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutConfigurationSetArchivingOptionsCommandInput,
    PutConfigurationSetArchivingOptionsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutConfigurationSetArchivingOptionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutConfigurationSetArchivingOptionsCommandInput,
    PutConfigurationSetArchivingOptionsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutConfigurationSetArchivingOptionsCommand extends PutConfigurationSetArchivingOptionsCommand_base {
  protected static __types: {
    api: {
      input: PutConfigurationSetArchivingOptionsRequest;
      output: {};
    };
    sdk: {
      input: PutConfigurationSetArchivingOptionsCommandInput;
      output: PutConfigurationSetArchivingOptionsCommandOutput;
    };
  };
}
