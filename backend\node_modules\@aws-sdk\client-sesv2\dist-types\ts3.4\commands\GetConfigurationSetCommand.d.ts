import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetConfigurationSetRequest,
  GetConfigurationSetResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetConfigurationSetCommandInput
  extends GetConfigurationSetRequest {}
export interface GetConfigurationSetCommandOutput
  extends GetConfigurationSetResponse,
    __MetadataBearer {}
declare const GetConfigurationSetCommand_base: {
  new (
    input: GetConfigurationSetCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetConfigurationSetCommandInput,
    GetConfigurationSetCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetConfigurationSetCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetConfigurationSetCommandInput,
    GetConfigurationSetCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetConfigurationSetCommand extends GetConfigurationSetCommand_base {
  protected static __types: {
    api: {
      input: GetConfigurationSetRequest;
      output: GetConfigurationSetResponse;
    };
    sdk: {
      input: GetConfigurationSetCommandInput;
      output: GetConfigurationSetCommandOutput;
    };
  };
}
