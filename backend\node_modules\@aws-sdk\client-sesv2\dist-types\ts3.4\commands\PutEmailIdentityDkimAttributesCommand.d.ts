import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutEmailIdentityDkimAttributesRequest,
  PutEmailIdentityDkimAttributesResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutEmailIdentityDkimAttributesCommandInput
  extends PutEmailIdentityDkimAttributesRequest {}
export interface PutEmailIdentityDkimAttributesCommandOutput
  extends PutEmailIdentityDkimAttributesResponse,
    __MetadataBearer {}
declare const PutEmailIdentityDkimAttributesCommand_base: {
  new (
    input: PutEmailIdentityDkimAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutEmailIdentityDkimAttributesCommandInput,
    PutEmailIdentityDkimAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutEmailIdentityDkimAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutEmailIdentityDkimAttributesCommandInput,
    PutEmailIdentityDkimAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutEmailIdentityDkimAttributesCommand extends PutEmailIdentityDkimAttributesCommand_base {
  protected static __types: {
    api: {
      input: PutEmailIdentityDkimAttributesRequest;
      output: {};
    };
    sdk: {
      input: PutEmailIdentityDkimAttributesCommandInput;
      output: PutEmailIdentityDkimAttributesCommandOutput;
    };
  };
}
