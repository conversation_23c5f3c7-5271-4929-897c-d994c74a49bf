import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { GetTenantRequest, GetTenantResponse } from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetTenantCommandInput extends GetTenantRequest {}
export interface GetTenantCommandOutput
  extends GetTenantResponse,
    __MetadataBearer {}
declare const GetTenantCommand_base: {
  new (
    input: GetTenantCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetTenantCommandInput,
    GetTenantCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetTenantCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetTenantCommandInput,
    GetTenantCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetTenantCommand extends GetTenantCommand_base {
  protected static __types: {
    api: {
      input: GetTenantRequest;
      output: GetTenantResponse;
    };
    sdk: {
      input: GetTenantCommandInput;
      output: GetTenantCommandOutput;
    };
  };
}
