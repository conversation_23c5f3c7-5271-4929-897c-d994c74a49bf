import { Paginator } from "@smithy/types";
import { ListExportJobsCommandInput, ListExportJobsCommandOutput } from "../commands/ListExportJobsCommand";
import { SESv2PaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListExportJobs: (config: SESv2PaginationConfiguration, input: ListExportJobsCommandInput, ...rest: any[]) => Paginator<ListExportJobsCommandOutput>;
