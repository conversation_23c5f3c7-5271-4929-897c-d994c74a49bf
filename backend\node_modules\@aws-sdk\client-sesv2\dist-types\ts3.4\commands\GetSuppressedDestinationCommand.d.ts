import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetSuppressedDestinationRequest,
  GetSuppressedDestinationResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetSuppressedDestinationCommandInput
  extends GetSuppressedDestinationRequest {}
export interface GetSuppressedDestinationCommandOutput
  extends GetSuppressedDestinationResponse,
    __MetadataBearer {}
declare const GetSuppressedDestinationCommand_base: {
  new (
    input: GetSuppressedDestinationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetSuppressedDestinationCommandInput,
    GetSuppressedDestinationCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetSuppressedDestinationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetSuppressedDestinationCommandInput,
    GetSuppressedDestinationCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetSuppressedDestinationCommand extends GetSuppressedDestinationCommand_base {
  protected static __types: {
    api: {
      input: GetSuppressedDestinationRequest;
      output: GetSuppressedDestinationResponse;
    };
    sdk: {
      input: GetSuppressedDestinationCommandInput;
      output: GetSuppressedDestinationCommandOutput;
    };
  };
}
