import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListResourceTenantsRequest,
  ListResourceTenantsResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface ListResourceTenantsCommandInput
  extends ListResourceTenantsRequest {}
export interface ListResourceTenantsCommandOutput
  extends ListResourceTenantsResponse,
    __MetadataBearer {}
declare const ListResourceTenantsCommand_base: {
  new (
    input: ListResourceTenantsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListResourceTenantsCommandInput,
    ListResourceTenantsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListResourceTenantsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListResourceTenantsCommandInput,
    ListResourceTenantsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListResourceTenantsCommand extends ListResourceTenantsCommand_base {
  protected static __types: {
    api: {
      input: ListResourceTenantsRequest;
      output: ListResourceTenantsResponse;
    };
    sdk: {
      input: ListResourceTenantsCommandInput;
      output: ListResourceTenantsCommandOutput;
    };
  };
}
