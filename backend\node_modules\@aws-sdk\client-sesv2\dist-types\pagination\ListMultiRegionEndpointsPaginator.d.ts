import { Paginator } from "@smithy/types";
import { ListMultiRegionEndpointsCommandInput, ListMultiRegionEndpointsCommandOutput } from "../commands/ListMultiRegionEndpointsCommand";
import { SESv2PaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListMultiRegionEndpoints: (config: SESv2PaginationConfiguration, input: ListMultiRegionEndpointsCommandInput, ...rest: any[]) => Paginator<ListMultiRegionEndpointsCommandOutput>;
