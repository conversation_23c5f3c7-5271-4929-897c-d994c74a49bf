import { Paginator } from "@smithy/types";
import { ListEmailTemplatesCommandInput, ListEmailTemplatesCommandOutput } from "../commands/ListEmailTemplatesCommand";
import { SESv2PaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListEmailTemplates: (config: SESv2PaginationConfiguration, input: ListEmailTemplatesCommandInput, ...rest: any[]) => Paginator<ListEmailTemplatesCommandOutput>;
