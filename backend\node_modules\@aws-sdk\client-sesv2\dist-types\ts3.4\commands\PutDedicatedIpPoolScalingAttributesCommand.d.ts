import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutDedicatedIpPoolScalingAttributesRequest,
  PutDedicatedIpPoolScalingAttributesResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutDedicatedIpPoolScalingAttributesCommandInput
  extends PutDedicatedIpPoolScalingAttributesRequest {}
export interface PutDedicatedIpPoolScalingAttributesCommandOutput
  extends PutDedicatedIpPoolScalingAttributesResponse,
    __MetadataBearer {}
declare const PutDedicatedIpPoolScalingAttributesCommand_base: {
  new (
    input: PutDedicatedIpPoolScalingAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutDedicatedIpPoolScalingAttributesCommandInput,
    PutDedicatedIpPoolScalingAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutDedicatedIpPoolScalingAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutDedicatedIpPoolScalingAttributesCommandInput,
    PutDedicatedIpPoolScalingAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutDedicatedIpPoolScalingAttributesCommand extends PutDedicatedIpPoolScalingAttributesCommand_base {
  protected static __types: {
    api: {
      input: PutDedicatedIpPoolScalingAttributesRequest;
      output: {};
    };
    sdk: {
      input: PutDedicatedIpPoolScalingAttributesCommandInput;
      output: PutDedicatedIpPoolScalingAttributesCommandOutput;
    };
  };
}
