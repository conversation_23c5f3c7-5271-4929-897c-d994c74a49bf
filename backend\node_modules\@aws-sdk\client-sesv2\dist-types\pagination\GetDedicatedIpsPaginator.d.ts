import { Paginator } from "@smithy/types";
import { GetDedicatedIpsCommandInput, GetDedicatedIpsCommandOutput } from "../commands/GetDedicatedIpsCommand";
import { SESv2PaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateGetDedicatedIps: (config: SESv2PaginationConfiguration, input: GetDedicatedIpsCommandInput, ...rest: any[]) => Paginator<GetDedicatedIpsCommandOutput>;
