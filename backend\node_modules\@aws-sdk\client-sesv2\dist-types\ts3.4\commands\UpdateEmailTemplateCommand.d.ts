import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateEmailTemplateRequest,
  UpdateEmailTemplateResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface UpdateEmailTemplateCommandInput
  extends UpdateEmailTemplateRequest {}
export interface UpdateEmailTemplateCommandOutput
  extends UpdateEmailTemplateResponse,
    __MetadataBearer {}
declare const UpdateEmailTemplateCommand_base: {
  new (
    input: UpdateEmailTemplateCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateEmailTemplateCommandInput,
    UpdateEmailTemplateCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateEmailTemplateCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateEmailTemplateCommandInput,
    UpdateEmailTemplateCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateEmailTemplateCommand extends UpdateEmailTemplateCommand_base {
  protected static __types: {
    api: {
      input: UpdateEmailTemplateRequest;
      output: {};
    };
    sdk: {
      input: UpdateEmailTemplateCommandInput;
      output: UpdateEmailTemplateCommandOutput;
    };
  };
}
