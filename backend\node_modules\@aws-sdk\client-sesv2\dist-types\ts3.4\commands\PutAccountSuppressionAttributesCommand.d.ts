import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutAccountSuppressionAttributesRequest,
  PutAccountSuppressionAttributesResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutAccountSuppressionAttributesCommandInput
  extends PutAccountSuppressionAttributesRequest {}
export interface PutAccountSuppressionAttributesCommandOutput
  extends PutAccountSuppressionAttributesResponse,
    __MetadataBearer {}
declare const PutAccountSuppressionAttributesCommand_base: {
  new (
    input: PutAccountSuppressionAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutAccountSuppressionAttributesCommandInput,
    PutAccountSuppressionAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [PutAccountSuppressionAttributesCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    PutAccountSuppressionAttributesCommandInput,
    PutAccountSuppressionAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutAccountSuppressionAttributesCommand extends PutAccountSuppressionAttributesCommand_base {
  protected static __types: {
    api: {
      input: PutAccountSuppressionAttributesRequest;
      output: {};
    };
    sdk: {
      input: PutAccountSuppressionAttributesCommandInput;
      output: PutAccountSuppressionAttributesCommandOutput;
    };
  };
}
