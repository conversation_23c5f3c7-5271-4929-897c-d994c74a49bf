{"version": 3, "sources": ["../../is-retry-allowed/index.js", "../../axios-retry/dist/esm/index.js"], "sourcesContent": ["'use strict';\n\nconst denyList = new Set([\n\t'ENOTFOUND',\n\t'ENE<PERSON><PERSON>EACH',\n\n\t// SSL errors from https://github.com/nodejs/node/blob/fc8e3e2cdc521978351de257030db0076d79e0ab/src/crypto/crypto_common.cc#L301-L328\n\t'UNABLE_TO_GET_ISSUER_CERT',\n\t'UNABLE_TO_GET_CRL',\n\t'UNABLE_TO_DECRYPT_CERT_SIGNATURE',\n\t'UNABLE_TO_DECRYPT_CRL_SIGNATURE',\n\t'UNABLE_TO_DECODE_ISSUER_PUBLIC_KEY',\n\t'CERT_SIGNATURE_FAILURE',\n\t'CRL_SIGNATURE_FAILURE',\n\t'CERT_NOT_YET_VALID',\n\t'CERT_HAS_EXPIRED',\n\t'CRL_NOT_YET_VALID',\n\t'CRL_HAS_EXPIRED',\n\t'ERROR_IN_CERT_NOT_BEFORE_FIELD',\n\t'ERROR_IN_CERT_NOT_AFTER_FIELD',\n\t'ERROR_IN_CRL_LAST_UPDATE_FIELD',\n\t'ERROR_IN_CRL_NEXT_UPDATE_FIELD',\n\t'OUT_OF_MEM',\n\t'DEPTH_ZERO_SELF_SIGNED_CERT',\n\t'SELF_SIGNED_CERT_IN_CHAIN',\n\t'UNABLE_TO_GET_ISSUER_CERT_LOCALLY',\n\t'UNABLE_TO_VERIFY_LEAF_SIGNATURE',\n\t'CERT_CHAIN_TOO_LONG',\n\t'CERT_REVOKED',\n\t'INVALID_CA',\n\t'PATH_LENGTH_EXCEEDED',\n\t'INVALID_PURPOSE',\n\t'CERT_UNTRUSTED',\n\t'CERT_REJECTED',\n\t'HOSTNAME_MISMATCH'\n]);\n\n// TODO: Use `error?.code` when targeting Node.js 14\nmodule.exports = error => !denyList.has(error && error.code);\n", "import isRetryAllowed from 'is-retry-allowed';\nexport const namespace = 'axios-retry';\nexport function isNetworkError(error) {\n    const CODE_EXCLUDE_LIST = ['ERR_CANCELED', 'ECONNABORTED'];\n    if (error.response) {\n        return false;\n    }\n    if (!error.code) {\n        return false;\n    }\n    // Prevents retrying timed out & cancelled requests\n    if (CODE_EXCLUDE_LIST.includes(error.code)) {\n        return false;\n    }\n    // Prevents retrying unsafe errors\n    return isRetryAllowed(error);\n}\nconst SAFE_HTTP_METHODS = ['get', 'head', 'options'];\nconst IDEMPOTENT_HTTP_METHODS = SAFE_HTTP_METHODS.concat(['put', 'delete']);\nexport function isRetryableError(error) {\n    return (error.code !== 'ECONNABORTED' &&\n        (!error.response ||\n            error.response.status === 429 ||\n            (error.response.status >= 500 && error.response.status <= 599)));\n}\nexport function isSafeRequestError(error) {\n    if (!error.config?.method) {\n        // Cannot determine if the request can be retried\n        return false;\n    }\n    return isRetryableError(error) && SAFE_HTTP_METHODS.indexOf(error.config.method) !== -1;\n}\nexport function isIdempotentRequestError(error) {\n    if (!error.config?.method) {\n        // Cannot determine if the request can be retried\n        return false;\n    }\n    return isRetryableError(error) && IDEMPOTENT_HTTP_METHODS.indexOf(error.config.method) !== -1;\n}\nexport function isNetworkOrIdempotentRequestError(error) {\n    return isNetworkError(error) || isIdempotentRequestError(error);\n}\nexport function retryAfter(error = undefined) {\n    const retryAfterHeader = error?.response?.headers['retry-after'];\n    if (!retryAfterHeader) {\n        return 0;\n    }\n    // if the retry after header is a number, convert it to milliseconds\n    let retryAfterMs = (Number(retryAfterHeader) || 0) * 1000;\n    // If the retry after header is a date, get the number of milliseconds until that date\n    if (retryAfterMs === 0) {\n        retryAfterMs = (new Date(retryAfterHeader).valueOf() || 0) - Date.now();\n    }\n    return Math.max(0, retryAfterMs);\n}\nfunction noDelay(_retryNumber = 0, error = undefined) {\n    return Math.max(0, retryAfter(error));\n}\nexport function exponentialDelay(retryNumber = 0, error = undefined, delayFactor = 100) {\n    const calculatedDelay = 2 ** retryNumber * delayFactor;\n    const delay = Math.max(calculatedDelay, retryAfter(error));\n    const randomSum = delay * 0.2 * Math.random(); // 0-20% of the delay\n    return delay + randomSum;\n}\n/**\n * Linear delay\n * @param {number | undefined} delayFactor - delay factor in milliseconds (default: 100)\n * @returns {function} (retryNumber: number, error: AxiosError | undefined) => number\n */\nexport function linearDelay(delayFactor = 100) {\n    return (retryNumber = 0, error = undefined) => {\n        const delay = retryNumber * delayFactor;\n        return Math.max(delay, retryAfter(error));\n    };\n}\nexport const DEFAULT_OPTIONS = {\n    retries: 3,\n    retryCondition: isNetworkOrIdempotentRequestError,\n    retryDelay: noDelay,\n    shouldResetTimeout: false,\n    onRetry: () => { },\n    onMaxRetryTimesExceeded: () => { },\n    validateResponse: null\n};\nfunction getRequestOptions(config, defaultOptions) {\n    return { ...DEFAULT_OPTIONS, ...defaultOptions, ...config[namespace] };\n}\nfunction setCurrentState(config, defaultOptions, resetLastRequestTime = false) {\n    const currentState = getRequestOptions(config, defaultOptions || {});\n    currentState.retryCount = currentState.retryCount || 0;\n    if (!currentState.lastRequestTime || resetLastRequestTime) {\n        currentState.lastRequestTime = Date.now();\n    }\n    config[namespace] = currentState;\n    return currentState;\n}\nfunction fixConfig(axiosInstance, config) {\n    // @ts-ignore\n    if (axiosInstance.defaults.agent === config.agent) {\n        // @ts-ignore\n        delete config.agent;\n    }\n    if (axiosInstance.defaults.httpAgent === config.httpAgent) {\n        delete config.httpAgent;\n    }\n    if (axiosInstance.defaults.httpsAgent === config.httpsAgent) {\n        delete config.httpsAgent;\n    }\n}\nasync function shouldRetry(currentState, error) {\n    const { retries, retryCondition } = currentState;\n    const shouldRetryOrPromise = (currentState.retryCount || 0) < retries && retryCondition(error);\n    // This could be a promise\n    if (typeof shouldRetryOrPromise === 'object') {\n        try {\n            const shouldRetryPromiseResult = await shouldRetryOrPromise;\n            // keep return true unless shouldRetryPromiseResult return false for compatibility\n            return shouldRetryPromiseResult !== false;\n        }\n        catch (_err) {\n            return false;\n        }\n    }\n    return shouldRetryOrPromise;\n}\nasync function handleRetry(axiosInstance, currentState, error, config) {\n    currentState.retryCount += 1;\n    const { retryDelay, shouldResetTimeout, onRetry } = currentState;\n    const delay = retryDelay(currentState.retryCount, error);\n    // Axios fails merging this configuration to the default configuration because it has an issue\n    // with circular structures: https://github.com/mzabriskie/axios/issues/370\n    fixConfig(axiosInstance, config);\n    if (!shouldResetTimeout && config.timeout && currentState.lastRequestTime) {\n        const lastRequestDuration = Date.now() - currentState.lastRequestTime;\n        const timeout = config.timeout - lastRequestDuration - delay;\n        if (timeout <= 0) {\n            return Promise.reject(error);\n        }\n        config.timeout = timeout;\n    }\n    config.transformRequest = [(data) => data];\n    await onRetry(currentState.retryCount, error, config);\n    if (config.signal?.aborted) {\n        return Promise.resolve(axiosInstance(config));\n    }\n    return new Promise((resolve) => {\n        const abortListener = () => {\n            clearTimeout(timeout);\n            resolve(axiosInstance(config));\n        };\n        const timeout = setTimeout(() => {\n            resolve(axiosInstance(config));\n            if (config.signal?.removeEventListener) {\n                config.signal.removeEventListener('abort', abortListener);\n            }\n        }, delay);\n        if (config.signal?.addEventListener) {\n            config.signal.addEventListener('abort', abortListener, { once: true });\n        }\n    });\n}\nasync function handleMaxRetryTimesExceeded(currentState, error) {\n    if (currentState.retryCount >= currentState.retries)\n        await currentState.onMaxRetryTimesExceeded(error, currentState.retryCount);\n}\nconst axiosRetry = (axiosInstance, defaultOptions) => {\n    const requestInterceptorId = axiosInstance.interceptors.request.use((config) => {\n        setCurrentState(config, defaultOptions, true);\n        if (config[namespace]?.validateResponse) {\n            // by setting this, all HTTP responses will be go through the error interceptor first\n            config.validateStatus = () => false;\n        }\n        return config;\n    });\n    const responseInterceptorId = axiosInstance.interceptors.response.use(null, async (error) => {\n        const { config } = error;\n        // If we have no information to retry the request\n        if (!config) {\n            return Promise.reject(error);\n        }\n        const currentState = setCurrentState(config, defaultOptions);\n        if (error.response && currentState.validateResponse?.(error.response)) {\n            // no issue with response\n            return error.response;\n        }\n        if (await shouldRetry(currentState, error)) {\n            return handleRetry(axiosInstance, currentState, error, config);\n        }\n        await handleMaxRetryTimesExceeded(currentState, error);\n        return Promise.reject(error);\n    });\n    return { requestInterceptorId, responseInterceptorId };\n};\n// Compatibility with CommonJS\naxiosRetry.isNetworkError = isNetworkError;\naxiosRetry.isSafeRequestError = isSafeRequestError;\naxiosRetry.isIdempotentRequestError = isIdempotentRequestError;\naxiosRetry.isNetworkOrIdempotentRequestError = isNetworkOrIdempotentRequestError;\naxiosRetry.exponentialDelay = exponentialDelay;\naxiosRetry.linearDelay = linearDelay;\naxiosRetry.isRetryableError = isRetryableError;\nexport default axiosRetry;\n"], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAEA,QAAM,WAAW,oBAAI,IAAI;AAAA,MACxB;AAAA,MACA;AAAA;AAAA,MAGA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAC;AAGD,WAAO,UAAU,WAAS,CAAC,SAAS,IAAI,SAAS,MAAM,IAAI;AAAA;AAAA;;;ACtC3D,8BAA2B;AACpB,IAAM,YAAY;AAClB,SAAS,eAAe,OAAO;AAClC,QAAM,oBAAoB,CAAC,gBAAgB,cAAc;AACzD,MAAI,MAAM,UAAU;AAChB,WAAO;AAAA,EACX;AACA,MAAI,CAAC,MAAM,MAAM;AACb,WAAO;AAAA,EACX;AAEA,MAAI,kBAAkB,SAAS,MAAM,IAAI,GAAG;AACxC,WAAO;AAAA,EACX;AAEA,aAAO,wBAAAA,SAAe,KAAK;AAC/B;AACA,IAAM,oBAAoB,CAAC,OAAO,QAAQ,SAAS;AACnD,IAAM,0BAA0B,kBAAkB,OAAO,CAAC,OAAO,QAAQ,CAAC;AACnE,SAAS,iBAAiB,OAAO;AACpC,SAAQ,MAAM,SAAS,mBAClB,CAAC,MAAM,YACJ,MAAM,SAAS,WAAW,OACzB,MAAM,SAAS,UAAU,OAAO,MAAM,SAAS,UAAU;AACtE;AACO,SAAS,mBAAmB,OAAO;AAzB1C;AA0BI,MAAI,GAAC,WAAM,WAAN,mBAAc,SAAQ;AAEvB,WAAO;AAAA,EACX;AACA,SAAO,iBAAiB,KAAK,KAAK,kBAAkB,QAAQ,MAAM,OAAO,MAAM,MAAM;AACzF;AACO,SAAS,yBAAyB,OAAO;AAhChD;AAiCI,MAAI,GAAC,WAAM,WAAN,mBAAc,SAAQ;AAEvB,WAAO;AAAA,EACX;AACA,SAAO,iBAAiB,KAAK,KAAK,wBAAwB,QAAQ,MAAM,OAAO,MAAM,MAAM;AAC/F;AACO,SAAS,kCAAkC,OAAO;AACrD,SAAO,eAAe,KAAK,KAAK,yBAAyB,KAAK;AAClE;AACO,SAAS,WAAW,QAAQ,QAAW;AA1C9C;AA2CI,QAAM,oBAAmB,oCAAO,aAAP,mBAAiB,QAAQ;AAClD,MAAI,CAAC,kBAAkB;AACnB,WAAO;AAAA,EACX;AAEA,MAAI,gBAAgB,OAAO,gBAAgB,KAAK,KAAK;AAErD,MAAI,iBAAiB,GAAG;AACpB,oBAAgB,IAAI,KAAK,gBAAgB,EAAE,QAAQ,KAAK,KAAK,KAAK,IAAI;AAAA,EAC1E;AACA,SAAO,KAAK,IAAI,GAAG,YAAY;AACnC;AACA,SAAS,QAAQ,eAAe,GAAG,QAAQ,QAAW;AAClD,SAAO,KAAK,IAAI,GAAG,WAAW,KAAK,CAAC;AACxC;AACO,SAAS,iBAAiB,cAAc,GAAG,QAAQ,QAAW,cAAc,KAAK;AACpF,QAAM,kBAAkB,KAAK,cAAc;AAC3C,QAAM,QAAQ,KAAK,IAAI,iBAAiB,WAAW,KAAK,CAAC;AACzD,QAAM,YAAY,QAAQ,MAAM,KAAK,OAAO;AAC5C,SAAO,QAAQ;AACnB;AAMO,SAAS,YAAY,cAAc,KAAK;AAC3C,SAAO,CAAC,cAAc,GAAG,QAAQ,WAAc;AAC3C,UAAM,QAAQ,cAAc;AAC5B,WAAO,KAAK,IAAI,OAAO,WAAW,KAAK,CAAC;AAAA,EAC5C;AACJ;AACO,IAAM,kBAAkB;AAAA,EAC3B,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,SAAS,MAAM;AAAA,EAAE;AAAA,EACjB,yBAAyB,MAAM;AAAA,EAAE;AAAA,EACjC,kBAAkB;AACtB;AACA,SAAS,kBAAkB,QAAQ,gBAAgB;AAC/C,SAAO,EAAE,GAAG,iBAAiB,GAAG,gBAAgB,GAAG,OAAO,SAAS,EAAE;AACzE;AACA,SAAS,gBAAgB,QAAQ,gBAAgB,uBAAuB,OAAO;AAC3E,QAAM,eAAe,kBAAkB,QAAQ,kBAAkB,CAAC,CAAC;AACnE,eAAa,aAAa,aAAa,cAAc;AACrD,MAAI,CAAC,aAAa,mBAAmB,sBAAsB;AACvD,iBAAa,kBAAkB,KAAK,IAAI;AAAA,EAC5C;AACA,SAAO,SAAS,IAAI;AACpB,SAAO;AACX;AACA,SAAS,UAAU,eAAe,QAAQ;AAEtC,MAAI,cAAc,SAAS,UAAU,OAAO,OAAO;AAE/C,WAAO,OAAO;AAAA,EAClB;AACA,MAAI,cAAc,SAAS,cAAc,OAAO,WAAW;AACvD,WAAO,OAAO;AAAA,EAClB;AACA,MAAI,cAAc,SAAS,eAAe,OAAO,YAAY;AACzD,WAAO,OAAO;AAAA,EAClB;AACJ;AACA,eAAe,YAAY,cAAc,OAAO;AAC5C,QAAM,EAAE,SAAS,eAAe,IAAI;AACpC,QAAM,wBAAwB,aAAa,cAAc,KAAK,WAAW,eAAe,KAAK;AAE7F,MAAI,OAAO,yBAAyB,UAAU;AAC1C,QAAI;AACA,YAAM,2BAA2B,MAAM;AAEvC,aAAO,6BAA6B;AAAA,IACxC,SACO,MAAM;AACT,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,eAAe,YAAY,eAAe,cAAc,OAAO,QAAQ;AA7HvE;AA8HI,eAAa,cAAc;AAC3B,QAAM,EAAE,YAAY,oBAAoB,QAAQ,IAAI;AACpD,QAAM,QAAQ,WAAW,aAAa,YAAY,KAAK;AAGvD,YAAU,eAAe,MAAM;AAC/B,MAAI,CAAC,sBAAsB,OAAO,WAAW,aAAa,iBAAiB;AACvE,UAAM,sBAAsB,KAAK,IAAI,IAAI,aAAa;AACtD,UAAM,UAAU,OAAO,UAAU,sBAAsB;AACvD,QAAI,WAAW,GAAG;AACd,aAAO,QAAQ,OAAO,KAAK;AAAA,IAC/B;AACA,WAAO,UAAU;AAAA,EACrB;AACA,SAAO,mBAAmB,CAAC,CAAC,SAAS,IAAI;AACzC,QAAM,QAAQ,aAAa,YAAY,OAAO,MAAM;AACpD,OAAI,YAAO,WAAP,mBAAe,SAAS;AACxB,WAAO,QAAQ,QAAQ,cAAc,MAAM,CAAC;AAAA,EAChD;AACA,SAAO,IAAI,QAAQ,CAAC,YAAY;AAjJpC,QAAAC;AAkJQ,UAAM,gBAAgB,MAAM;AACxB,mBAAa,OAAO;AACpB,cAAQ,cAAc,MAAM,CAAC;AAAA,IACjC;AACA,UAAM,UAAU,WAAW,MAAM;AAtJzC,UAAAA;AAuJY,cAAQ,cAAc,MAAM,CAAC;AAC7B,WAAIA,MAAA,OAAO,WAAP,gBAAAA,IAAe,qBAAqB;AACpC,eAAO,OAAO,oBAAoB,SAAS,aAAa;AAAA,MAC5D;AAAA,IACJ,GAAG,KAAK;AACR,SAAIA,MAAA,OAAO,WAAP,gBAAAA,IAAe,kBAAkB;AACjC,aAAO,OAAO,iBAAiB,SAAS,eAAe,EAAE,MAAM,KAAK,CAAC;AAAA,IACzE;AAAA,EACJ,CAAC;AACL;AACA,eAAe,4BAA4B,cAAc,OAAO;AAC5D,MAAI,aAAa,cAAc,aAAa;AACxC,UAAM,aAAa,wBAAwB,OAAO,aAAa,UAAU;AACjF;AACA,IAAM,aAAa,CAAC,eAAe,mBAAmB;AAClD,QAAM,uBAAuB,cAAc,aAAa,QAAQ,IAAI,CAAC,WAAW;AAtKpF;AAuKQ,oBAAgB,QAAQ,gBAAgB,IAAI;AAC5C,SAAI,YAAO,SAAS,MAAhB,mBAAmB,kBAAkB;AAErC,aAAO,iBAAiB,MAAM;AAAA,IAClC;AACA,WAAO;AAAA,EACX,CAAC;AACD,QAAM,wBAAwB,cAAc,aAAa,SAAS,IAAI,MAAM,OAAO,UAAU;AA9KjG;AA+KQ,UAAM,EAAE,OAAO,IAAI;AAEnB,QAAI,CAAC,QAAQ;AACT,aAAO,QAAQ,OAAO,KAAK;AAAA,IAC/B;AACA,UAAM,eAAe,gBAAgB,QAAQ,cAAc;AAC3D,QAAI,MAAM,cAAY,kBAAa,qBAAb,sCAAgC,MAAM,YAAW;AAEnE,aAAO,MAAM;AAAA,IACjB;AACA,QAAI,MAAM,YAAY,cAAc,KAAK,GAAG;AACxC,aAAO,YAAY,eAAe,cAAc,OAAO,MAAM;AAAA,IACjE;AACA,UAAM,4BAA4B,cAAc,KAAK;AACrD,WAAO,QAAQ,OAAO,KAAK;AAAA,EAC/B,CAAC;AACD,SAAO,EAAE,sBAAsB,sBAAsB;AACzD;AAEA,WAAW,iBAAiB;AAC5B,WAAW,qBAAqB;AAChC,WAAW,2BAA2B;AACtC,WAAW,oCAAoC;AAC/C,WAAW,mBAAmB;AAC9B,WAAW,cAAc;AACzB,WAAW,mBAAmB;AAC9B,IAAO,cAAQ;", "names": ["isRetryAllowed", "_a"]}