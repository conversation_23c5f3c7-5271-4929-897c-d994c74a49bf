import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListCustomVerificationEmailTemplatesRequest,
  ListCustomVerificationEmailTemplatesResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface ListCustomVerificationEmailTemplatesCommandInput
  extends ListCustomVerificationEmailTemplatesRequest {}
export interface ListCustomVerificationEmailTemplatesCommandOutput
  extends ListCustomVerificationEmailTemplatesResponse,
    __MetadataBearer {}
declare const ListCustomVerificationEmailTemplatesCommand_base: {
  new (
    input: ListCustomVerificationEmailTemplatesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListCustomVerificationEmailTemplatesCommandInput,
    ListCustomVerificationEmailTemplatesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListCustomVerificationEmailTemplatesCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListCustomVerificationEmailTemplatesCommandInput,
    ListCustomVerificationEmailTemplatesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListCustomVerificationEmailTemplatesCommand extends ListCustomVerificationEmailTemplatesCommand_base {
  protected static __types: {
    api: {
      input: ListCustomVerificationEmailTemplatesRequest;
      output: ListCustomVerificationEmailTemplatesResponse;
    };
    sdk: {
      input: ListCustomVerificationEmailTemplatesCommandInput;
      output: ListCustomVerificationEmailTemplatesCommandOutput;
    };
  };
}
