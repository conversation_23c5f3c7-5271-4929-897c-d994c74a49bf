import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetDomainStatisticsReportRequest,
  GetDomainStatisticsReportResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetDomainStatisticsReportCommandInput
  extends GetDomainStatisticsReportRequest {}
export interface GetDomainStatisticsReportCommandOutput
  extends GetDomainStatisticsReportResponse,
    __MetadataBearer {}
declare const GetDomainStatisticsReportCommand_base: {
  new (
    input: GetDomainStatisticsReportCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetDomainStatisticsReportCommandInput,
    GetDomainStatisticsReportCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetDomainStatisticsReportCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetDomainStatisticsReportCommandInput,
    GetDomainStatisticsReportCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetDomainStatisticsReportCommand extends GetDomainStatisticsReportCommand_base {
  protected static __types: {
    api: {
      input: GetDomainStatisticsReportRequest;
      output: GetDomainStatisticsReportResponse;
    };
    sdk: {
      input: GetDomainStatisticsReportCommandInput;
      output: GetDomainStatisticsReportCommandOutput;
    };
  };
}
