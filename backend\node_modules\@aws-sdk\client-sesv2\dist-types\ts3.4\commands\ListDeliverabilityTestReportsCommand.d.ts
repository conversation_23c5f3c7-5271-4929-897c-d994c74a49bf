import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListDeliverabilityTestReportsRequest,
  ListDeliverabilityTestReportsResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface ListDeliverabilityTestReportsCommandInput
  extends ListDeliverabilityTestReportsRequest {}
export interface ListDeliverabilityTestReportsCommandOutput
  extends ListDeliverabilityTestReportsResponse,
    __MetadataBearer {}
declare const ListDeliverabilityTestReportsCommand_base: {
  new (
    input: ListDeliverabilityTestReportsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListDeliverabilityTestReportsCommandInput,
    ListDeliverabilityTestReportsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListDeliverabilityTestReportsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListDeliverabilityTestReportsCommandInput,
    ListDeliverabilityTestReportsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListDeliverabilityTestReportsCommand extends ListDeliverabilityTestReportsCommand_base {
  protected static __types: {
    api: {
      input: ListDeliverabilityTestReportsRequest;
      output: ListDeliverabilityTestReportsResponse;
    };
    sdk: {
      input: ListDeliverabilityTestReportsCommandInput;
      output: ListDeliverabilityTestReportsCommandOutput;
    };
  };
}
