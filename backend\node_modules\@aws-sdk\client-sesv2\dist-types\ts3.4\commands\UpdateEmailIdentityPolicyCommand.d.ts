import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateEmailIdentityPolicyRequest,
  UpdateEmailIdentityPolicyResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface UpdateEmailIdentityPolicyCommandInput
  extends UpdateEmailIdentityPolicyRequest {}
export interface UpdateEmailIdentityPolicyCommandOutput
  extends UpdateEmailIdentityPolicyResponse,
    __MetadataBearer {}
declare const UpdateEmailIdentityPolicyCommand_base: {
  new (
    input: UpdateEmailIdentityPolicyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateEmailIdentityPolicyCommandInput,
    UpdateEmailIdentityPolicyCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateEmailIdentityPolicyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateEmailIdentityPolicyCommandInput,
    UpdateEmailIdentityPolicyCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateEmailIdentityPolicyCommand extends UpdateEmailIdentityPolicyCommand_base {
  protected static __types: {
    api: {
      input: UpdateEmailIdentityPolicyRequest;
      output: {};
    };
    sdk: {
      input: UpdateEmailIdentityPolicyCommandInput;
      output: UpdateEmailIdentityPolicyCommandOutput;
    };
  };
}
