import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutConfigurationSetSendingOptionsRequest,
  PutConfigurationSetSendingOptionsResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutConfigurationSetSendingOptionsCommandInput
  extends PutConfigurationSetSendingOptionsRequest {}
export interface PutConfigurationSetSendingOptionsCommandOutput
  extends PutConfigurationSetSendingOptionsResponse,
    __MetadataBearer {}
declare const PutConfigurationSetSendingOptionsCommand_base: {
  new (
    input: PutConfigurationSetSendingOptionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutConfigurationSetSendingOptionsCommandInput,
    PutConfigurationSetSendingOptionsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutConfigurationSetSendingOptionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutConfigurationSetSendingOptionsCommandInput,
    PutConfigurationSetSendingOptionsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutConfigurationSetSendingOptionsCommand extends PutConfigurationSetSendingOptionsCommand_base {
  protected static __types: {
    api: {
      input: PutConfigurationSetSendingOptionsRequest;
      output: {};
    };
    sdk: {
      input: PutConfigurationSetSendingOptionsCommandInput;
      output: PutConfigurationSetSendingOptionsCommandOutput;
    };
  };
}
