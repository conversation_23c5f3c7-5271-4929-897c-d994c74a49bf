import { Paginator } from "@smithy/types";
import { ListCustomVerificationEmailTemplatesCommandInput, ListCustomVerificationEmailTemplatesCommandOutput } from "../commands/ListCustomVerificationEmailTemplatesCommand";
import { SESv2PaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListCustomVerificationEmailTemplates: (config: SESv2PaginationConfiguration, input: ListCustomVerificationEmailTemplatesCommandInput, ...rest: any[]) => Paginator<ListCustomVerificationEmailTemplatesCommandOutput>;
