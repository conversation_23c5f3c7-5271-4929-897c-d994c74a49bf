import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetEmailTemplateRequest,
  GetEmailTemplateResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetEmailTemplateCommandInput extends GetEmailTemplateRequest {}
export interface GetEmailTemplateCommandOutput
  extends GetEmailTemplateResponse,
    __MetadataBearer {}
declare const GetEmailTemplateCommand_base: {
  new (
    input: GetEmailTemplateCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetEmailTemplateCommandInput,
    GetEmailTemplateCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetEmailTemplateCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetEmailTemplateCommandInput,
    GetEmailTemplateCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetEmailTemplateCommand extends GetEmailTemplateCommand_base {
  protected static __types: {
    api: {
      input: GetEmailTemplateRequest;
      output: GetEmailTemplateResponse;
    };
    sdk: {
      input: GetEmailTemplateCommandInput;
      output: GetEmailTemplateCommandOutput;
    };
  };
}
