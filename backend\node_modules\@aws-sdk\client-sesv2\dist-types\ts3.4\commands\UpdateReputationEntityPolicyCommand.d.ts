import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateReputationEntityPolicyRequest,
  UpdateReputationEntityPolicyResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface UpdateReputationEntityPolicyCommandInput
  extends UpdateReputationEntityPolicyRequest {}
export interface UpdateReputationEntityPolicyCommandOutput
  extends UpdateReputationEntityPolicyResponse,
    __MetadataBearer {}
declare const UpdateReputationEntityPolicyCommand_base: {
  new (
    input: UpdateReputationEntityPolicyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateReputationEntityPolicyCommandInput,
    UpdateReputationEntityPolicyCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateReputationEntityPolicyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateReputationEntityPolicyCommandInput,
    UpdateReputationEntityPolicyCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateReputationEntityPolicyCommand extends UpdateReputationEntityPolicyCommand_base {
  protected static __types: {
    api: {
      input: UpdateReputationEntityPolicyRequest;
      output: {};
    };
    sdk: {
      input: UpdateReputationEntityPolicyCommandInput;
      output: UpdateReputationEntityPolicyCommandOutput;
    };
  };
}
