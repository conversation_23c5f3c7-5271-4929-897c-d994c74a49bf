import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListDomainDeliverabilityCampaignsRequest,
  ListDomainDeliverabilityCampaignsResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface ListDomainDeliverabilityCampaignsCommandInput
  extends ListDomainDeliverabilityCampaignsRequest {}
export interface ListDomainDeliverabilityCampaignsCommandOutput
  extends ListDomainDeliverabilityCampaignsResponse,
    __MetadataBearer {}
declare const ListDomainDeliverabilityCampaignsCommand_base: {
  new (
    input: ListDomainDeliverabilityCampaignsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListDomainDeliverabilityCampaignsCommandInput,
    ListDomainDeliverabilityCampaignsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListDomainDeliverabilityCampaignsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListDomainDeliverabilityCampaignsCommandInput,
    ListDomainDeliverabilityCampaignsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListDomainDeliverabilityCampaignsCommand extends ListDomainDeliverabilityCampaignsCommand_base {
  protected static __types: {
    api: {
      input: ListDomainDeliverabilityCampaignsRequest;
      output: ListDomainDeliverabilityCampaignsResponse;
    };
    sdk: {
      input: ListDomainDeliverabilityCampaignsCommandInput;
      output: ListDomainDeliverabilityCampaignsCommandOutput;
    };
  };
}
