import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetDeliverabilityDashboardOptionsRequest,
  GetDeliverabilityDashboardOptionsResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetDeliverabilityDashboardOptionsCommandInput
  extends GetDeliverabilityDashboardOptionsRequest {}
export interface GetDeliverabilityDashboardOptionsCommandOutput
  extends GetDeliverabilityDashboardOptionsResponse,
    __MetadataBearer {}
declare const GetDeliverabilityDashboardOptionsCommand_base: {
  new (
    input: GetDeliverabilityDashboardOptionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetDeliverabilityDashboardOptionsCommandInput,
    GetDeliverabilityDashboardOptionsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [GetDeliverabilityDashboardOptionsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    GetDeliverabilityDashboardOptionsCommandInput,
    GetDeliverabilityDashboardOptionsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetDeliverabilityDashboardOptionsCommand extends GetDeliverabilityDashboardOptionsCommand_base {
  protected static __types: {
    api: {
      input: {};
      output: GetDeliverabilityDashboardOptionsResponse;
    };
    sdk: {
      input: GetDeliverabilityDashboardOptionsCommandInput;
      output: GetDeliverabilityDashboardOptionsCommandOutput;
    };
  };
}
