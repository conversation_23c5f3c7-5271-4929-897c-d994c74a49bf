import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListTenantResourcesRequest,
  ListTenantResourcesResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface ListTenantResourcesCommandInput
  extends ListTenantResourcesRequest {}
export interface ListTenantResourcesCommandOutput
  extends ListTenantResourcesResponse,
    __MetadataBearer {}
declare const ListTenantResourcesCommand_base: {
  new (
    input: ListTenantResourcesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListTenantResourcesCommandInput,
    ListTenantResourcesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListTenantResourcesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListTenantResourcesCommandInput,
    ListTenantResourcesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListTenantResourcesCommand extends ListTenantResourcesCommand_base {
  protected static __types: {
    api: {
      input: ListTenantResourcesRequest;
      output: ListTenantResourcesResponse;
    };
    sdk: {
      input: ListTenantResourcesCommandInput;
      output: ListTenantResourcesCommandOutput;
    };
  };
}
