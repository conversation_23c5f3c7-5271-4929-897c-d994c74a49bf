import { Paginator } from "@smithy/types";
import { ListResourceTenantsCommandInput, ListResourceTenantsCommandOutput } from "../commands/ListResourceTenantsCommand";
import { SESv2PaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListResourceTenants: (config: SESv2PaginationConfiguration, input: ListResourceTenantsCommandInput, ...rest: any[]) => Paginator<ListResourceTenantsCommandOutput>;
