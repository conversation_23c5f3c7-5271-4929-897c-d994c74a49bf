import { Paginator } from "@smithy/types";
import { ListDeliverabilityTestReportsCommandInput, ListDeliverabilityTestReportsCommandOutput } from "../commands/ListDeliverabilityTestReportsCommand";
import { SESv2PaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListDeliverabilityTestReports: (config: SESv2PaginationConfiguration, input: ListDeliverabilityTestReportsCommandInput, ...rest: any[]) => Paginator<ListDeliverabilityTestReportsCommandOutput>;
