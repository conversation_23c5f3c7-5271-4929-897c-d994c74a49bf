import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutDeliverabilityDashboardOptionRequest,
  PutDeliverabilityDashboardOptionResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutDeliverabilityDashboardOptionCommandInput
  extends PutDeliverabilityDashboardOptionRequest {}
export interface PutDeliverabilityDashboardOptionCommandOutput
  extends PutDeliverabilityDashboardOptionResponse,
    __MetadataBearer {}
declare const PutDeliverabilityDashboardOptionCommand_base: {
  new (
    input: PutDeliverabilityDashboardOptionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutDeliverabilityDashboardOptionCommandInput,
    PutDeliverabilityDashboardOptionCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutDeliverabilityDashboardOptionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutDeliverabilityDashboardOptionCommandInput,
    PutDeliverabilityDashboardOptionCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutDeliverabilityDashboardOptionCommand extends PutDeliverabilityDashboardOptionCommand_base {
  protected static __types: {
    api: {
      input: PutDeliverabilityDashboardOptionRequest;
      output: {};
    };
    sdk: {
      input: PutDeliverabilityDashboardOptionCommandInput;
      output: PutDeliverabilityDashboardOptionCommandOutput;
    };
  };
}
