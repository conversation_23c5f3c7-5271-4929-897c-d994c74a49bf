import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutConfigurationSetVdmOptionsRequest,
  PutConfigurationSetVdmOptionsResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutConfigurationSetVdmOptionsCommandInput
  extends PutConfigurationSetVdmOptionsRequest {}
export interface PutConfigurationSetVdmOptionsCommandOutput
  extends PutConfigurationSetVdmOptionsResponse,
    __MetadataBearer {}
declare const PutConfigurationSetVdmOptionsCommand_base: {
  new (
    input: PutConfigurationSetVdmOptionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutConfigurationSetVdmOptionsCommandInput,
    PutConfigurationSetVdmOptionsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutConfigurationSetVdmOptionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutConfigurationSetVdmOptionsCommandInput,
    PutConfigurationSetVdmOptionsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutConfigurationSetVdmOptionsCommand extends PutConfigurationSetVdmOptionsCommand_base {
  protected static __types: {
    api: {
      input: PutConfigurationSetVdmOptionsRequest;
      output: {};
    };
    sdk: {
      input: PutConfigurationSetVdmOptionsCommandInput;
      output: PutConfigurationSetVdmOptionsCommandOutput;
    };
  };
}
