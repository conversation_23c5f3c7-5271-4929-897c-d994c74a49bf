import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutDedicatedIpWarmupAttributesRequest,
  PutDedicatedIpWarmupAttributesResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutDedicatedIpWarmupAttributesCommandInput
  extends PutDedicatedIpWarmupAttributesRequest {}
export interface PutDedicatedIpWarmupAttributesCommandOutput
  extends PutDedicatedIpWarmupAttributesResponse,
    __MetadataBearer {}
declare const PutDedicatedIpWarmupAttributesCommand_base: {
  new (
    input: PutDedicatedIpWarmupAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutDedicatedIpWarmupAttributesCommandInput,
    PutDedicatedIpWarmupAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutDedicatedIpWarmupAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutDedicatedIpWarmupAttributesCommandInput,
    PutDedicatedIpWarmupAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutDedicatedIpWarmupAttributesCommand extends PutDedicatedIpWarmupAttributesCommand_base {
  protected static __types: {
    api: {
      input: PutDedicatedIpWarmupAttributesRequest;
      output: {};
    };
    sdk: {
      input: PutDedicatedIpWarmupAttributesCommandInput;
      output: PutDedicatedIpWarmupAttributesCommandOutput;
    };
  };
}
