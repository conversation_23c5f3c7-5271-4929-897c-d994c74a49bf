{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-16 20:51:01'
}
{
  service: 'vue-frontend-backend',
  clientVersion: '6.14.0',
  errorCode: 'P1003',
  retryable: undefined,
  name: 'PrismaClientInitializationError',
  level: 'error',
  message: '数据库连接失败: Database `vue_frontend_db` does not exist',
  stack: 'PrismaClientInitializationError: Database `vue_frontend_db` does not exist\n' +
    '    at r (E:\\12312\\backend\\node_modules\\@prisma\\client\\src\\runtime\\core\\engines\\library\\LibraryEngine.ts:440:17)\n' +
    '    at async connectDatabase (E:\\12312\\backend\\src\\config\\database.ts:50:5)\n' +
    '    at async startServer (E:\\12312\\backend\\src\\app.ts:90:5)',
  timestamp: '2025-08-16 20:51:01'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-16 20:52:24'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-16 20:52:24'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-16 20:52:24'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-16 20:52:24'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-16 20:52:24'
}
{
  message: '时间: 2025-08-16T12:52:24.120Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-16 20:52:24'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:48:03'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:48:03'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:48:03'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:48:03'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:48:03'
}
{
  message: '时间: 2025-08-17T06:48:03.060Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:48:03'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/health 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/api/health',
  method: 'GET',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Augment-VSCode/1.0',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/health 未找到',
  timestamp: '2025-08-17 14:48:38'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/auth/login 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:688:15\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:276:14)\n' +
    '    at Function.handle (E:\\12312\\backend\\node_modules\\router\\index.js:186:3)\n' +
    '    at router (E:\\12312\\backend\\node_modules\\router\\index.js:60:12)',
  url: '/api/auth/login',
  method: 'GET',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Augment-VSCode/1.0',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/auth/login 未找到',
  timestamp: '2025-08-17 14:49:13'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:53:07'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:53:07'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:53:07'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:53:07'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:53:07'
}
{
  message: '时间: 2025-08-17T06:53:07.517Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:53:07'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:53:18'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:53:18'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:53:18'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:53:18'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:53:18'
}
{
  message: '时间: 2025-08-17T06:53:18.295Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 14:53:18'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/auth/login 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:688:15\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:276:14)\n' +
    '    at Function.handle (E:\\12312\\backend\\node_modules\\router\\index.js:186:3)\n' +
    '    at router (E:\\12312\\backend\\node_modules\\router\\index.js:60:12)',
  url: '/api/auth/login',
  method: 'GET',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Augment-VSCode/1.0',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/auth/login 未找到',
  timestamp: '2025-08-17 14:53:43'
}
{
  service: 'vue-frontend-backend',
  userId: 'cmee9b7ns002jlq3kho1gycr7',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.6216',
  level: 'info',
  message: '用户登录成功: <EMAIL>',
  timestamp: '2025-08-17 14:54:02'
}
{
  service: 'vue-frontend-backend',
  userId: 'cmee9b7ns002jlq3kho1gycr7',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.6216',
  level: 'info',
  message: '用户登录成功: <EMAIL>',
  timestamp: '2025-08-17 14:54:10'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 15:00:47'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 15:00:47'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 15:00:47'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 15:00:47'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 15:00:47'
}
{
  message: '时间: 2025-08-17T07:00:47.063Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 15:00:47'
}
{
  service: 'vue-frontend-backend',
  userId: 'cmee9b7ns002jlq3kho1gycr7',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: '用户登录成功: <EMAIL>',
  timestamp: '2025-08-17 15:01:13'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 16:07:59'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 16:07:59'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 16:07:59'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 16:07:59'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 16:07:59'
}
{
  message: '时间: 2025-08-17T08:07:59.943Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 16:07:59'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 16:30:08'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 16:30:08'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 16:30:08'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 16:30:08'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 16:30:08'
}
{
  message: '时间: 2025-08-17T08:30:08.204Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 16:30:08'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /s 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/s',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /s 未找到',
  timestamp: '2025-08-17 17:58:44'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 20:26:52'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 20:26:52'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 20:26:52'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 20:26:52'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 20:26:52'
}
{
  message: '时间: 2025-08-17T12:26:52.740Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-17 20:26:52'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /s 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/s',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /s 未找到',
  timestamp: '2025-08-17 20:28:18'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /apis 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/apis',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /apis 未找到',
  timestamp: '2025-08-17 20:29:32'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /favicon.ico 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/favicon.ico',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /favicon.ico 未找到',
  timestamp: '2025-08-17 20:30:27'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /apis 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/apis',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /apis 未找到',
  timestamp: '2025-08-17 20:31:30'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /apis 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/apis',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /apis 未找到',
  timestamp: '2025-08-17 20:33:30'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /apis 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/apis',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /apis 未找到',
  timestamp: '2025-08-17 20:34:25'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /apis 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/apis',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /apis 未找到',
  timestamp: '2025-08-17 20:35:01'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /apis 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/apis',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /apis 未找到',
  timestamp: '2025-08-17 20:35:47'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:52:28'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:52:28'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:52:28'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:52:28'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:52:28'
}
{
  message: '时间: 2025-08-17T16:52:28.460Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:52:28'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:53:29'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:53:29'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:53:29'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:53:29'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:53:29'
}
{
  message: '时间: 2025-08-17T16:53:29.697Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:53:29'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:53:40'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:53:40'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:53:40'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:53:40'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:53:40'
}
{
  message: '时间: 2025-08-17T16:53:40.436Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 00:53:40'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/health 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/api/health',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.6216',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/health 未找到',
  timestamp: '2025-08-18 00:57:34'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 01:03:00'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 01:03:00'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 01:03:00'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 01:03:00'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 01:03:00'
}
{
  message: '时间: 2025-08-17T17:03:00.758Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 01:03:00'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 / 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 / 未找到',
  timestamp: '2025-08-18 01:04:32'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /favicon.ico 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/favicon.ico',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /favicon.ico 未找到',
  timestamp: '2025-08-18 01:04:32'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/health 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/api/health',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/health 未找到',
  timestamp: '2025-08-18 01:04:51'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/health 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/api/health',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/health 未找到',
  timestamp: '2025-08-18 01:04:53'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 01:05:34'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 01:05:34'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 01:05:34'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 01:05:34'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 01:05:34'
}
{
  message: '时间: 2025-08-17T17:05:34.458Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 01:05:34'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/health 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/api/health',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/health 未找到',
  timestamp: '2025-08-18 01:44:05'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 02:14:09'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 02:14:09'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 02:14:09'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 02:14:09'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 02:14:09'
}
{
  message: '时间: 2025-08-17T18:14:09.029Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 02:14:09'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 02:14:48'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 02:14:48'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 02:14:48'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 02:14:48'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 02:14:48'
}
{
  message: '时间: 2025-08-17T18:14:48.703Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 02:14:48'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc 未找到',
  timestamp: '2025-08-18 02:15:24'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:18:26'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:18:26'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:18:26'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:18:26'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:18:26'
}
{
  message: '时间: 2025-08-17T19:18:26.788Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:18:26'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 邮箱或密码错误\n' +
    '    at AuthController.login (E:\\12312\\backend\\dist\\controllers\\AuthController.js:115:19)',
  url: '/api/auth/login',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 邮箱或密码错误',
  timestamp: '2025-08-18 03:20:13'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 邮箱或密码错误\n' +
    '    at AuthController.login (E:\\12312\\backend\\dist\\controllers\\AuthController.js:115:19)',
  url: '/api/auth/login',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 邮箱或密码错误',
  timestamp: '2025-08-18 03:21:40'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:27:04'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:27:04'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:27:04'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:27:04'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:27:04'
}
{
  message: '时间: 2025-08-17T19:27:04.160Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:27:04'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 邮箱或密码错误\n' +
    '    at AuthController.login (E:\\12312\\backend\\dist\\controllers\\AuthController.js:115:19)',
  url: '/api/auth/login',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.6216',
  userId: undefined,
  level: 'error',
  message: '错误: 邮箱或密码错误',
  timestamp: '2025-08-18 03:28:23'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:29:11'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:29:11'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:29:11'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:29:11'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:29:11'
}
{
  message: '时间: 2025-08-17T19:29:11.833Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:29:11'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:30:59'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:30:59'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:30:59'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:30:59'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:30:59'
}
{
  message: '时间: 2025-08-17T19:30:59.377Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:30:59'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:32:07'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:32:07'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:32:07'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:32:07'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:32:07'
}
{
  message: '时间: 2025-08-17T19:32:07.367Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:32:07'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:32:18'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:32:18'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:32:18'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:32:18'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:32:18'
}
{
  message: '时间: 2025-08-17T19:32:18.802Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 03:32:18'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 邮箱或密码错误\n' +
    '    at AuthController.login (E:\\12312\\backend\\src\\controllers\\AuthController.ts:146:13)',
  url: '/api/auth/login',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 邮箱或密码错误',
  timestamp: '2025-08-18 03:34:07'
}
{
  service: 'vue-frontend-backend',
  userId: 'cmefy14ht0000lq64t47lucu0',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: '用户登录成功: <EMAIL>',
  timestamp: '2025-08-18 03:39:28'
}
{
  service: 'vue-frontend-backend',
  userId: 'cmefy14ht0000lq64t47lucu0',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: '用户登录成功: <EMAIL>',
  timestamp: '2025-08-18 12:17:37'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 请求体验证失败: "author" is not allowed\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\validation.ts:45:13\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\lib\\route.js:157:13)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\lib\\route.js:157:13)\n' +
    '    at authenticate (E:\\12312\\backend\\src\\middlewares\\auth.ts:106:5)',
  url: '/api/apis',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: 'cmefy14ht0000lq64t47lucu0',
  level: 'error',
  message: '错误: 请求体验证失败: "author" is not allowed',
  timestamp: '2025-08-18 12:40:13'
}
{
  message: 'Prisma Error: \n' +
    'Invalid `prisma.api.create()` invocation in\n' +
    'E:\\12312\\backend\\src\\controllers\\ApiController.ts:308:34\n' +
    '\n' +
    '  305 }\n' +
    '  306 \n' +
    '  307 // 创建API\n' +
    '→ 308 const api = await prisma.api.create(\n' +
    'Foreign key constraint violated on the fields: (`categoryId`)',
  level: 'error',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 12:44:03'
}
{
  service: 'vue-frontend-backend',
  stack: 'PrismaClientKnownRequestError: \n' +
    'Invalid `prisma.api.create()` invocation in\n' +
    'E:\\12312\\backend\\src\\controllers\\ApiController.ts:308:34\n' +
    '\n' +
    '  305 }\n' +
    '  306 \n' +
    '  307 // 创建API\n' +
    '→ 308 const api = await prisma.api.create(\n' +
    'Foreign key constraint violated on the fields: (`categoryId`)\n' +
    '    at ei.handleRequestError (E:\\12312\\backend\\node_modules\\@prisma\\client\\src\\runtime\\RequestHandler.ts:228:13)\n' +
    '    at ei.handleAndLogRequestError (E:\\12312\\backend\\node_modules\\@prisma\\client\\src\\runtime\\RequestHandler.ts:174:12)\n' +
    '    at ei.request (E:\\12312\\backend\\node_modules\\@prisma\\client\\src\\runtime\\RequestHandler.ts:143:12)\n' +
    '    at async a (E:\\12312\\backend\\node_modules\\@prisma\\client\\src\\runtime\\getPrismaClient.ts:833:24)\n' +
    '    at async ApiController.submitApi (E:\\12312\\backend\\src\\controllers\\ApiController.ts:308:17)',
  url: '/api/apis',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: 'cmefy14ht0000lq64t47lucu0',
  level: 'error',
  message: '错误: \n' +
    'Invalid `prisma.api.create()` invocation in\n' +
    'E:\\12312\\backend\\src\\controllers\\ApiController.ts:308:34\n' +
    '\n' +
    '  305 }\n' +
    '  306 \n' +
    '  307 // 创建API\n' +
    '→ 308 const api = await prisma.api.create(\n' +
    'Foreign key constraint violated on the fields: (`categoryId`)',
  timestamp: '2025-08-18 12:44:03'
}
{
  service: 'vue-frontend-backend',
  userId: 'cmefy14ht0000lq64t47lucu0',
  apiId: 'cmegmwcno0009lquky79hv7qt',
  url: 'https://api.final.test/v1/submit',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'API提交: 最终测试API',
  timestamp: '2025-08-18 12:48:02'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc 未找到',
  timestamp: '2025-08-18 13:03:12'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc 未找到',
  timestamp: '2025-08-18 13:49:53'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/api/search?query=%E5%A4%A9%E6%B0%94&page=1&limit=12 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/api/api/search?query=%E5%A4%A9%E6%B0%94&page=1&limit=12',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/api/search?query=%E5%A4%A9%E6%B0%94&page=1&limit=12 未找到',
  timestamp: '2025-08-18 13:50:02'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc 未找到',
  timestamp: '2025-08-18 13:51:39'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 请求体验证失败: 邮箱是必需的\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\validation.ts:45:13\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\lib\\route.js:157:13)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\lib\\route.js:157:13)\n' +
    '    at Route.dispatch (E:\\12312\\backend\\node_modules\\router\\lib\\route.js:117:3)\n' +
    '    at handle (E:\\12312\\backend\\node_modules\\router\\index.js:435:11)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:295:15',
  url: '/api/auth/register',
  method: 'POST',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'error',
  message: '错误: 请求体验证失败: 邮箱是必需的',
  timestamp: '2025-08-18 14:14:39'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 需要提供认证令牌\n' +
    '    at authenticate (E:\\12312\\backend\\src\\middlewares\\auth.ts:77:13)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\lib\\route.js:157:13)\n' +
    '    at Route.dispatch (E:\\12312\\backend\\node_modules\\router\\lib\\route.js:117:3)\n' +
    '    at handle (E:\\12312\\backend\\node_modules\\router\\index.js:435:11)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:295:15\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at Function.handle (E:\\12312\\backend\\node_modules\\router\\index.js:186:3)',
  url: '/api/apis',
  method: 'POST',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'error',
  message: '错误: 需要提供认证令牌',
  timestamp: '2025-08-18 14:14:39'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /apis 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/apis',
  method: 'GET',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /apis 未找到',
  timestamp: '2025-08-18 14:14:39'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:26:27'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:26:27'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:26:27'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:26:27'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:26:27'
}
{
  message: '时间: 2025-08-18T06:26:27.050Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:26:27'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 请求体验证失败: 邮箱是必需的\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\validation.ts:45:13\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\lib\\route.js:157:13)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\lib\\route.js:157:13)\n' +
    '    at Route.dispatch (E:\\12312\\backend\\node_modules\\router\\lib\\route.js:117:3)\n' +
    '    at handle (E:\\12312\\backend\\node_modules\\router\\index.js:435:11)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:295:15',
  url: '/api/auth/login',
  method: 'POST',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'error',
  message: '错误: 请求体验证失败: 邮箱是必需的',
  timestamp: '2025-08-18 14:26:56'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 邮箱或密码错误\n' +
    '    at AuthController.login (E:\\12312\\backend\\src\\controllers\\AuthController.ts:115:13)',
  url: '/api/auth/login',
  method: 'POST',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'error',
  message: '错误: 邮箱或密码错误',
  timestamp: '2025-08-18 14:26:56'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc 未找到',
  timestamp: '2025-08-18 14:32:05'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc 未找到',
  timestamp: '2025-08-18 14:34:10'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/api/apis?page=1&limit=12&sortBy=createdAt&sortOrder=desc 未找到',
  timestamp: '2025-08-18 14:34:12'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/api/search?query=%E9%9A%8F%E6%9C%BA&page=1&limit=12 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/api/api/search?query=%E9%9A%8F%E6%9C%BA&page=1&limit=12',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/api/search?query=%E9%9A%8F%E6%9C%BA&page=1&limit=12 未找到',
  timestamp: '2025-08-18 14:34:47'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:47:42'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:47:42'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:47:42'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:47:42'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:47:42'
}
{
  message: '时间: 2025-08-18T06:47:42.854Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:47:42'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:48:40'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:48:40'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:48:40'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:48:40'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:48:40'
}
{
  message: '时间: 2025-08-18T06:48:40.102Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:48:40'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:49:01'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:49:01'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:49:01'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:49:01'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:49:01'
}
{
  message: '时间: 2025-08-18T06:49:01.372Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:49:01'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:51:11'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:51:11'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:51:11'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:51:11'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:51:11'
}
{
  message: '时间: 2025-08-18T06:51:11.859Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 14:51:11'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 15:08:22'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 15:08:22'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 15:08:22'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 15:08:22'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 15:08:22'
}
{
  message: '时间: 2025-08-18T07:08:22.431Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 15:08:22'
}
{
  service: 'vue-frontend-backend',
  errno: -4047,
  code: 'EPIPE',
  syscall: 'write',
  level: 'error',
  message: '未捕获的异常: write EPIPE',
  stack: 'Error: write EPIPE\n' +
    '    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n' +
    '    at writeGeneric (node:internal/stream_base_commons:150:3)\n' +
    '    at WriteStream.Socket._writeGeneric (node:net:966:11)\n' +
    '    at WriteStream.Socket._write (node:net:978:8)\n' +
    '    at writeOrBuffer (node:internal/streams/writable:572:12)\n' +
    '    at _write (node:internal/streams/writable:501:10)\n' +
    '    at WriteStream.Writable.write (node:internal/streams/writable:510:10)\n' +
    '    at Array.logRequest (E:\\12312\\backend\\node_modules\\morgan\\index.js:130:14)\n' +
    '    at listener (E:\\12312\\backend\\node_modules\\morgan\\node_modules\\on-finished\\index.js:169:15)\n' +
    '    at onFinish (E:\\12312\\backend\\node_modules\\morgan\\node_modules\\on-finished\\index.js:100:5)',
  timestamp: '2025-08-18 16:57:11'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:23:37'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:23:37'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:23:37'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:23:37'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:23:37'
}
{
  message: '时间: 2025-08-18T09:23:37.673Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:23:37'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:25:10'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:25:10'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:25:10'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:25:10'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:25:10'
}
{
  message: '时间: 2025-08-18T09:25:10.920Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:25:10'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /api/health 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/api/health',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.6216',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /api/health 未找到',
  timestamp: '2025-08-18 17:25:29'
}
{
  service: 'vue-frontend-backend',
  stack: 'Error: 路径 /favicon.ico 未找到\n' +
    '    at notFoundHandler (E:\\12312\\backend\\src\\middlewares\\errorHandler.ts:111:17)\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9\n' +
    '    at processParams (E:\\12312\\backend\\node_modules\\router\\index.js:582:12)\n' +
    '    at next (E:\\12312\\backend\\node_modules\\router\\index.js:291:5)\n' +
    '    at E:\\12312\\backend\\src\\middlewares\\rateLimiter.ts:103:5\n' +
    '    at Layer.handleRequest (E:\\12312\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n' +
    '    at trimPrefix (E:\\12312\\backend\\node_modules\\router\\index.js:342:13)\n' +
    '    at E:\\12312\\backend\\node_modules\\router\\index.js:297:9',
  url: '/favicon.ico',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  userId: undefined,
  level: 'error',
  message: '错误: 路径 /favicon.ico 未找到',
  timestamp: '2025-08-18 17:26:56'
}
{
  message: 'Prisma Info: Starting a mysql pool with 17 connections.',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:37:18'
}
{
  message: '数据库连接成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:37:18'
}
{
  message: '服务器启动成功',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:37:18'
}
{
  message: '端口: 8080',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:37:18'
}
{
  message: '环境: development',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:37:18'
}
{
  message: '时间: 2025-08-18T09:37:18.049Z',
  level: 'info',
  service: 'vue-frontend-backend',
  timestamp: '2025-08-18 17:37:18'
}
