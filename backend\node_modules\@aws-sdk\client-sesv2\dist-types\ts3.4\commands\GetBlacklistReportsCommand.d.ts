import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetBlacklistReportsRequest,
  GetBlacklistReportsResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetBlacklistReportsCommandInput
  extends GetBlacklistReportsRequest {}
export interface GetBlacklistReportsCommandOutput
  extends GetBlacklistReportsResponse,
    __MetadataBearer {}
declare const GetBlacklistReportsCommand_base: {
  new (
    input: GetBlacklistReportsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBlacklistReportsCommandInput,
    GetBlacklistReportsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetBlacklistReportsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBlacklistReportsCommandInput,
    GetBlacklistReportsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetBlacklistReportsCommand extends GetBlacklistReportsCommand_base {
  protected static __types: {
    api: {
      input: GetBlacklistReportsRequest;
      output: GetBlacklistReportsResponse;
    };
    sdk: {
      input: GetBlacklistReportsCommandInput;
      output: GetBlacklistReportsCommandOutput;
    };
  };
}
