import { Paginator } from "@smithy/types";
import { ListReputationEntitiesCommandInput, ListReputationEntitiesCommandOutput } from "../commands/ListReputationEntitiesCommand";
import { SESv2PaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListReputationEntities: (config: SESv2PaginationConfiguration, input: ListReputationEntitiesCommandInput, ...rest: any[]) => Paginator<ListReputationEntitiesCommandOutput>;
