import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetReputationEntityRequest,
  GetReputationEntityResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetReputationEntityCommandInput
  extends GetReputationEntityRequest {}
export interface GetReputationEntityCommandOutput
  extends GetReputationEntityResponse,
    __MetadataBearer {}
declare const GetReputationEntityCommand_base: {
  new (
    input: GetReputationEntityCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetReputationEntityCommandInput,
    GetReputationEntityCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetReputationEntityCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetReputationEntityCommandInput,
    GetReputationEntityCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetReputationEntityCommand extends GetReputationEntityCommand_base {
  protected static __types: {
    api: {
      input: GetReputationEntityRequest;
      output: GetReputationEntityResponse;
    };
    sdk: {
      input: GetReputationEntityCommandInput;
      output: GetReputationEntityCommandOutput;
    };
  };
}
