import { Paginator } from "@smithy/types";
import { ListSuppressedDestinationsCommandInput, ListSuppressedDestinationsCommandOutput } from "../commands/ListSuppressedDestinationsCommand";
import { SESv2PaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListSuppressedDestinations: (config: SESv2PaginationConfiguration, input: ListSuppressedDestinationsCommandInput, ...rest: any[]) => Paginator<ListSuppressedDestinationsCommandOutput>;
