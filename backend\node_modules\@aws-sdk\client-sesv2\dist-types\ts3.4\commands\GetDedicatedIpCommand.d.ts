import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetDedicatedIpRequest,
  GetDedicatedIpResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetDedicatedIpCommandInput extends GetDedicatedIpRequest {}
export interface GetDedicatedIpCommandOutput
  extends GetDedicatedIpResponse,
    __MetadataBearer {}
declare const GetDedicatedIpCommand_base: {
  new (
    input: GetDedicatedIpCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetDedicatedIpCommandInput,
    GetDedicatedIpCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetDedicatedIpCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetDedicatedIpCommandInput,
    GetDedicatedIpCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetDedicatedIpCommand extends GetDedicatedIpCommand_base {
  protected static __types: {
    api: {
      input: GetDedicatedIpRequest;
      output: GetDedicatedIpResponse;
    };
    sdk: {
      input: GetDedicatedIpCommandInput;
      output: GetDedicatedIpCommandOutput;
    };
  };
}
