import { Paginator } from "@smithy/types";
import { ListImportJobsCommandInput, ListImportJobsCommandOutput } from "../commands/ListImportJobsCommand";
import { SESv2PaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListImportJobs: (config: SESv2PaginationConfiguration, input: ListImportJobsCommandInput, ...rest: any[]) => Paginator<ListImportJobsCommandOutput>;
