import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { GetImportJobRequest, GetImportJobResponse } from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetImportJobCommandInput extends GetImportJobRequest {}
export interface GetImportJobCommandOutput
  extends GetImportJobResponse,
    __MetadataBearer {}
declare const GetImportJobCommand_base: {
  new (
    input: GetImportJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetImportJobCommandInput,
    GetImportJobCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetImportJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetImportJobCommandInput,
    GetImportJobCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetImportJobCommand extends GetImportJobCommand_base {
  protected static __types: {
    api: {
      input: GetImportJobRequest;
      output: GetImportJobResponse;
    };
    sdk: {
      input: GetImportJobCommandInput;
      output: GetImportJobCommandOutput;
    };
  };
}
