import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetDedicatedIpPoolRequest,
  GetDedicatedIpPoolResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetDedicatedIpPoolCommandInput
  extends GetDedicatedIpPoolRequest {}
export interface GetDedicatedIpPoolCommandOutput
  extends GetDedicatedIpPoolResponse,
    __MetadataBearer {}
declare const GetDedicatedIpPoolCommand_base: {
  new (
    input: GetDedicatedIpPoolCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetDedicatedIpPoolCommandInput,
    GetDedicatedIpPoolCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetDedicatedIpPoolCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetDedicatedIpPoolCommandInput,
    GetDedicatedIpPoolCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetDedicatedIpPoolCommand extends GetDedicatedIpPoolCommand_base {
  protected static __types: {
    api: {
      input: GetDedicatedIpPoolRequest;
      output: GetDedicatedIpPoolResponse;
    };
    sdk: {
      input: GetDedicatedIpPoolCommandInput;
      output: GetDedicatedIpPoolCommandOutput;
    };
  };
}
