import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutEmailIdentityMailFromAttributesRequest,
  PutEmailIdentityMailFromAttributesResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutEmailIdentityMailFromAttributesCommandInput
  extends PutEmailIdentityMailFromAttributesRequest {}
export interface PutEmailIdentityMailFromAttributesCommandOutput
  extends PutEmailIdentityMailFromAttributesResponse,
    __MetadataBearer {}
declare const PutEmailIdentityMailFromAttributesCommand_base: {
  new (
    input: PutEmailIdentityMailFromAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutEmailIdentityMailFromAttributesCommandInput,
    PutEmailIdentityMailFromAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutEmailIdentityMailFromAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutEmailIdentityMailFromAttributesCommandInput,
    PutEmailIdentityMailFromAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutEmailIdentityMailFromAttributesCommand extends PutEmailIdentityMailFromAttributesCommand_base {
  protected static __types: {
    api: {
      input: PutEmailIdentityMailFromAttributesRequest;
      output: {};
    };
    sdk: {
      input: PutEmailIdentityMailFromAttributesCommandInput;
      output: PutEmailIdentityMailFromAttributesCommandOutput;
    };
  };
}
