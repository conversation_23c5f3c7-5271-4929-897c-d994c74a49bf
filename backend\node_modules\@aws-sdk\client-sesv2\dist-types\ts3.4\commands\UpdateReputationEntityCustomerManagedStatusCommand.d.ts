import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateReputationEntityCustomerManagedStatusRequest,
  UpdateReputationEntityCustomerManagedStatusResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface UpdateReputationEntityCustomerManagedStatusCommandInput
  extends UpdateReputationEntityCustomerManagedStatusRequest {}
export interface UpdateReputationEntityCustomerManagedStatusCommandOutput
  extends UpdateReputationEntityCustomerManagedStatusResponse,
    __MetadataBearer {}
declare const UpdateReputationEntityCustomerManagedStatusCommand_base: {
  new (
    input: UpdateReputationEntityCustomerManagedStatusCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateReputationEntityCustomerManagedStatusCommandInput,
    UpdateReputationEntityCustomerManagedStatusCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateReputationEntityCustomerManagedStatusCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateReputationEntityCustomerManagedStatusCommandInput,
    UpdateReputationEntityCustomerManagedStatusCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateReputationEntityCustomerManagedStatusCommand extends UpdateReputationEntityCustomerManagedStatusCommand_base {
  protected static __types: {
    api: {
      input: UpdateReputationEntityCustomerManagedStatusRequest;
      output: {};
    };
    sdk: {
      input: UpdateReputationEntityCustomerManagedStatusCommandInput;
      output: UpdateReputationEntityCustomerManagedStatusCommandOutput;
    };
  };
}
