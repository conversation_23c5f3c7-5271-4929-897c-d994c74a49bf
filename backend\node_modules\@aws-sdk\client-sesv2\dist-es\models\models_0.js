import { SENSITIVE_STRING } from "@smithy/smithy-client";
import { SESv2ServiceException as __BaseException } from "./SESv2ServiceException";
export const ContactLanguage = {
    EN: "EN",
    JA: "JA",
};
export const MailType = {
    MARKETING: "MARKETING",
    TRANSACTIONAL: "TRANSACTIONAL",
};
export const ReviewStatus = {
    DENIED: "DENIED",
    FAILED: "FAILED",
    GRANTED: "GRANTED",
    PENDING: "PENDING",
};
export class AccountSuspendedException extends __BaseException {
    name = "AccountSuspendedException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "AccountSuspendedException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, AccountSuspendedException.prototype);
    }
}
export class AlreadyExistsException extends __BaseException {
    name = "AlreadyExistsException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "AlreadyExistsException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, AlreadyExistsException.prototype);
    }
}
export const AttachmentContentDisposition = {
    ATTACHMENT: "ATTACHMENT",
    INLINE: "INLINE",
};
export const AttachmentContentTransferEncoding = {
    BASE64: "BASE64",
    QUOTED_PRINTABLE: "QUOTED_PRINTABLE",
    SEVEN_BIT: "SEVEN_BIT",
};
export class BadRequestException extends __BaseException {
    name = "BadRequestException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "BadRequestException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, BadRequestException.prototype);
    }
}
export const MetricDimensionName = {
    CONFIGURATION_SET: "CONFIGURATION_SET",
    EMAIL_IDENTITY: "EMAIL_IDENTITY",
    ISP: "ISP",
};
export const Metric = {
    CLICK: "CLICK",
    COMPLAINT: "COMPLAINT",
    DELIVERY: "DELIVERY",
    DELIVERY_CLICK: "DELIVERY_CLICK",
    DELIVERY_COMPLAINT: "DELIVERY_COMPLAINT",
    DELIVERY_OPEN: "DELIVERY_OPEN",
    OPEN: "OPEN",
    PERMANENT_BOUNCE: "PERMANENT_BOUNCE",
    SEND: "SEND",
    TRANSIENT_BOUNCE: "TRANSIENT_BOUNCE",
};
export const MetricNamespace = {
    VDM: "VDM",
};
export const QueryErrorCode = {
    ACCESS_DENIED: "ACCESS_DENIED",
    INTERNAL_FAILURE: "INTERNAL_FAILURE",
};
export class InternalServiceErrorException extends __BaseException {
    name = "InternalServiceErrorException";
    $fault = "server";
    constructor(opts) {
        super({
            name: "InternalServiceErrorException",
            $fault: "server",
            ...opts,
        });
        Object.setPrototypeOf(this, InternalServiceErrorException.prototype);
    }
}
export class NotFoundException extends __BaseException {
    name = "NotFoundException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "NotFoundException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, NotFoundException.prototype);
    }
}
export class TooManyRequestsException extends __BaseException {
    name = "TooManyRequestsException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "TooManyRequestsException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, TooManyRequestsException.prototype);
    }
}
export const BehaviorOnMxFailure = {
    REJECT_MESSAGE: "REJECT_MESSAGE",
    USE_DEFAULT_VALUE: "USE_DEFAULT_VALUE",
};
export const BounceType = {
    PERMANENT: "PERMANENT",
    TRANSIENT: "TRANSIENT",
    UNDETERMINED: "UNDETERMINED",
};
export const BulkEmailStatus = {
    ACCOUNT_DAILY_QUOTA_EXCEEDED: "ACCOUNT_DAILY_QUOTA_EXCEEDED",
    ACCOUNT_SENDING_PAUSED: "ACCOUNT_SENDING_PAUSED",
    ACCOUNT_SUSPENDED: "ACCOUNT_SUSPENDED",
    ACCOUNT_THROTTLED: "ACCOUNT_THROTTLED",
    CONFIGURATION_SET_NOT_FOUND: "CONFIGURATION_SET_NOT_FOUND",
    CONFIGURATION_SET_SENDING_PAUSED: "CONFIGURATION_SET_SENDING_PAUSED",
    FAILED: "FAILED",
    INVALID_PARAMETER: "INVALID_PARAMETER",
    INVALID_SENDING_POOL_NAME: "INVALID_SENDING_POOL_NAME",
    MAIL_FROM_DOMAIN_NOT_VERIFIED: "MAIL_FROM_DOMAIN_NOT_VERIFIED",
    MESSAGE_REJECTED: "MESSAGE_REJECTED",
    SUCCESS: "SUCCESS",
    TEMPLATE_NOT_FOUND: "TEMPLATE_NOT_FOUND",
    TRANSIENT_FAILURE: "TRANSIENT_FAILURE",
};
export const DimensionValueSource = {
    EMAIL_HEADER: "EMAIL_HEADER",
    LINK_TAG: "LINK_TAG",
    MESSAGE_TAG: "MESSAGE_TAG",
};
export class ConcurrentModificationException extends __BaseException {
    name = "ConcurrentModificationException";
    $fault = "server";
    constructor(opts) {
        super({
            name: "ConcurrentModificationException",
            $fault: "server",
            ...opts,
        });
        Object.setPrototypeOf(this, ConcurrentModificationException.prototype);
    }
}
export class ConflictException extends __BaseException {
    name = "ConflictException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "ConflictException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ConflictException.prototype);
    }
}
export const SubscriptionStatus = {
    OPT_IN: "OPT_IN",
    OPT_OUT: "OPT_OUT",
};
export const ContactListImportAction = {
    DELETE: "DELETE",
    PUT: "PUT",
};
export const TlsPolicy = {
    OPTIONAL: "OPTIONAL",
    REQUIRE: "REQUIRE",
};
export const SuppressionListReason = {
    BOUNCE: "BOUNCE",
    COMPLAINT: "COMPLAINT",
};
export const HttpsPolicy = {
    OPTIONAL: "OPTIONAL",
    REQUIRE: "REQUIRE",
    REQUIRE_OPEN_ONLY: "REQUIRE_OPEN_ONLY",
};
export const FeatureStatus = {
    DISABLED: "DISABLED",
    ENABLED: "ENABLED",
};
export class LimitExceededException extends __BaseException {
    name = "LimitExceededException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "LimitExceededException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, LimitExceededException.prototype);
    }
}
export const EventType = {
    BOUNCE: "BOUNCE",
    CLICK: "CLICK",
    COMPLAINT: "COMPLAINT",
    DELIVERY: "DELIVERY",
    DELIVERY_DELAY: "DELIVERY_DELAY",
    OPEN: "OPEN",
    REJECT: "REJECT",
    RENDERING_FAILURE: "RENDERING_FAILURE",
    SEND: "SEND",
    SUBSCRIPTION: "SUBSCRIPTION",
};
export const ScalingMode = {
    MANAGED: "MANAGED",
    STANDARD: "STANDARD",
};
export const DeliverabilityTestStatus = {
    COMPLETED: "COMPLETED",
    IN_PROGRESS: "IN_PROGRESS",
};
export class MailFromDomainNotVerifiedException extends __BaseException {
    name = "MailFromDomainNotVerifiedException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "MailFromDomainNotVerifiedException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, MailFromDomainNotVerifiedException.prototype);
    }
}
export class MessageRejected extends __BaseException {
    name = "MessageRejected";
    $fault = "client";
    constructor(opts) {
        super({
            name: "MessageRejected",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, MessageRejected.prototype);
    }
}
export class SendingPausedException extends __BaseException {
    name = "SendingPausedException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "SendingPausedException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, SendingPausedException.prototype);
    }
}
export const DkimSigningAttributesOrigin = {
    AWS_SES: "AWS_SES",
    AWS_SES_AF_SOUTH_1: "AWS_SES_AF_SOUTH_1",
    AWS_SES_AP_NORTHEAST_1: "AWS_SES_AP_NORTHEAST_1",
    AWS_SES_AP_NORTHEAST_2: "AWS_SES_AP_NORTHEAST_2",
    AWS_SES_AP_NORTHEAST_3: "AWS_SES_AP_NORTHEAST_3",
    AWS_SES_AP_SOUTHEAST_1: "AWS_SES_AP_SOUTHEAST_1",
    AWS_SES_AP_SOUTHEAST_2: "AWS_SES_AP_SOUTHEAST_2",
    AWS_SES_AP_SOUTHEAST_3: "AWS_SES_AP_SOUTHEAST_3",
    AWS_SES_AP_SOUTH_1: "AWS_SES_AP_SOUTH_1",
    AWS_SES_AP_SOUTH_2: "AWS_SES_AP_SOUTH_2",
    AWS_SES_CA_CENTRAL_1: "AWS_SES_CA_CENTRAL_1",
    AWS_SES_EU_CENTRAL_1: "AWS_SES_EU_CENTRAL_1",
    AWS_SES_EU_CENTRAL_2: "AWS_SES_EU_CENTRAL_2",
    AWS_SES_EU_NORTH_1: "AWS_SES_EU_NORTH_1",
    AWS_SES_EU_SOUTH_1: "AWS_SES_EU_SOUTH_1",
    AWS_SES_EU_WEST_1: "AWS_SES_EU_WEST_1",
    AWS_SES_EU_WEST_2: "AWS_SES_EU_WEST_2",
    AWS_SES_EU_WEST_3: "AWS_SES_EU_WEST_3",
    AWS_SES_IL_CENTRAL_1: "AWS_SES_IL_CENTRAL_1",
    AWS_SES_ME_CENTRAL_1: "AWS_SES_ME_CENTRAL_1",
    AWS_SES_ME_SOUTH_1: "AWS_SES_ME_SOUTH_1",
    AWS_SES_SA_EAST_1: "AWS_SES_SA_EAST_1",
    AWS_SES_US_EAST_1: "AWS_SES_US_EAST_1",
    AWS_SES_US_EAST_2: "AWS_SES_US_EAST_2",
    AWS_SES_US_WEST_1: "AWS_SES_US_WEST_1",
    AWS_SES_US_WEST_2: "AWS_SES_US_WEST_2",
    EXTERNAL: "EXTERNAL",
};
export const DkimSigningKeyLength = {
    RSA_1024_BIT: "RSA_1024_BIT",
    RSA_2048_BIT: "RSA_2048_BIT",
};
export const DkimStatus = {
    FAILED: "FAILED",
    NOT_STARTED: "NOT_STARTED",
    PENDING: "PENDING",
    SUCCESS: "SUCCESS",
    TEMPORARY_FAILURE: "TEMPORARY_FAILURE",
};
export const IdentityType = {
    DOMAIN: "DOMAIN",
    EMAIL_ADDRESS: "EMAIL_ADDRESS",
    MANAGED_DOMAIN: "MANAGED_DOMAIN",
};
export const DeliveryEventType = {
    COMPLAINT: "COMPLAINT",
    DELIVERY: "DELIVERY",
    PERMANENT_BOUNCE: "PERMANENT_BOUNCE",
    SEND: "SEND",
    TRANSIENT_BOUNCE: "TRANSIENT_BOUNCE",
    UNDETERMINED_BOUNCE: "UNDETERMINED_BOUNCE",
};
export const EngagementEventType = {
    CLICK: "CLICK",
    OPEN: "OPEN",
};
export const MetricAggregation = {
    RATE: "RATE",
    VOLUME: "VOLUME",
};
export const DataFormat = {
    CSV: "CSV",
    JSON: "JSON",
};
export const SuppressionListImportAction = {
    DELETE: "DELETE",
    PUT: "PUT",
};
export const Status = {
    CREATING: "CREATING",
    DELETING: "DELETING",
    FAILED: "FAILED",
    READY: "READY",
};
export const SendingStatus = {
    DISABLED: "DISABLED",
    ENABLED: "ENABLED",
    REINSTATED: "REINSTATED",
};
export const WarmupStatus = {
    DONE: "DONE",
    IN_PROGRESS: "IN_PROGRESS",
    NOT_APPLICABLE: "NOT_APPLICABLE",
};
export const DeliverabilityDashboardAccountStatus = {
    ACTIVE: "ACTIVE",
    DISABLED: "DISABLED",
    PENDING_EXPIRATION: "PENDING_EXPIRATION",
};
export const ExportSourceType = {
    MESSAGE_INSIGHTS: "MESSAGE_INSIGHTS",
    METRICS_DATA: "METRICS_DATA",
};
export const JobStatus = {
    CANCELLED: "CANCELLED",
    COMPLETED: "COMPLETED",
    CREATED: "CREATED",
    FAILED: "FAILED",
    PROCESSING: "PROCESSING",
};
export const MailFromDomainStatus = {
    FAILED: "FAILED",
    PENDING: "PENDING",
    SUCCESS: "SUCCESS",
    TEMPORARY_FAILURE: "TEMPORARY_FAILURE",
};
export const VerificationError = {
    DNS_SERVER_ERROR: "DNS_SERVER_ERROR",
    HOST_NOT_FOUND: "HOST_NOT_FOUND",
    INVALID_VALUE: "INVALID_VALUE",
    REPLICATION_ACCESS_DENIED: "REPLICATION_ACCESS_DENIED",
    REPLICATION_PRIMARY_BYO_DKIM_NOT_SUPPORTED: "REPLICATION_PRIMARY_BYO_DKIM_NOT_SUPPORTED",
    REPLICATION_PRIMARY_INVALID_REGION: "REPLICATION_PRIMARY_INVALID_REGION",
    REPLICATION_PRIMARY_NOT_FOUND: "REPLICATION_PRIMARY_NOT_FOUND",
    REPLICATION_REPLICA_AS_PRIMARY_NOT_SUPPORTED: "REPLICATION_REPLICA_AS_PRIMARY_NOT_SUPPORTED",
    SERVICE_ERROR: "SERVICE_ERROR",
    TYPE_NOT_FOUND: "TYPE_NOT_FOUND",
};
export const VerificationStatus = {
    FAILED: "FAILED",
    NOT_STARTED: "NOT_STARTED",
    PENDING: "PENDING",
    SUCCESS: "SUCCESS",
    TEMPORARY_FAILURE: "TEMPORARY_FAILURE",
};
export const ReputationEntityType = {
    RESOURCE: "RESOURCE",
};
export const RecommendationImpact = {
    HIGH: "HIGH",
    LOW: "LOW",
};
export const ImportDestinationType = {
    CONTACT_LIST: "CONTACT_LIST",
    SUPPRESSION_LIST: "SUPPRESSION_LIST",
};
export class InvalidNextTokenException extends __BaseException {
    name = "InvalidNextTokenException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "InvalidNextTokenException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, InvalidNextTokenException.prototype);
    }
}
export const ListRecommendationsFilterKey = {
    IMPACT: "IMPACT",
    RESOURCE_ARN: "RESOURCE_ARN",
    STATUS: "STATUS",
    TYPE: "TYPE",
};
export const RecommendationStatus = {
    FIXED: "FIXED",
    OPEN: "OPEN",
};
export const RecommendationType = {
    BIMI: "BIMI",
    BOUNCE: "BOUNCE",
    COMPLAINT: "COMPLAINT",
    DKIM: "DKIM",
    DMARC: "DMARC",
    FEEDBACK_3P: "FEEDBACK_3P",
    IP_LISTING: "IP_LISTING",
    SPF: "SPF",
};
export const ReputationEntityFilterKey = {
    ENTITY_REFERENCE_PREFIX: "ENTITY_REFERENCE_PREFIX",
    ENTITY_TYPE: "ENTITY_TYPE",
    REPUTATION_IMPACT: "REPUTATION_IMPACT",
    STATUS: "SENDING_STATUS",
};
export const AccountDetailsFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.WebsiteURL && { WebsiteURL: SENSITIVE_STRING }),
    ...(obj.UseCaseDescription && { UseCaseDescription: SENSITIVE_STRING }),
    ...(obj.AdditionalContactEmailAddresses && { AdditionalContactEmailAddresses: SENSITIVE_STRING }),
});
export const DkimSigningAttributesFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.DomainSigningPrivateKey && { DomainSigningPrivateKey: SENSITIVE_STRING }),
});
export const CreateEmailIdentityRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.DkimSigningAttributes && {
        DkimSigningAttributes: DkimSigningAttributesFilterSensitiveLog(obj.DkimSigningAttributes),
    }),
});
export const MessageInsightsFiltersFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.FromEmailAddress && { FromEmailAddress: SENSITIVE_STRING }),
    ...(obj.Destination && { Destination: SENSITIVE_STRING }),
    ...(obj.Subject && { Subject: SENSITIVE_STRING }),
});
export const MessageInsightsDataSourceFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.Include && { Include: MessageInsightsFiltersFilterSensitiveLog(obj.Include) }),
    ...(obj.Exclude && { Exclude: MessageInsightsFiltersFilterSensitiveLog(obj.Exclude) }),
});
export const ExportDataSourceFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.MessageInsightsDataSource && {
        MessageInsightsDataSource: MessageInsightsDataSourceFilterSensitiveLog(obj.MessageInsightsDataSource),
    }),
});
export const CreateExportJobRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.ExportDataSource && { ExportDataSource: ExportDataSourceFilterSensitiveLog(obj.ExportDataSource) }),
});
export const EmailInsightsFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.Destination && { Destination: SENSITIVE_STRING }),
});
export const GetAccountResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.Details && { Details: AccountDetailsFilterSensitiveLog(obj.Details) }),
});
export const GetExportJobResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.ExportDataSource && { ExportDataSource: ExportDataSourceFilterSensitiveLog(obj.ExportDataSource) }),
});
export const GetMessageInsightsResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.FromEmailAddress && { FromEmailAddress: SENSITIVE_STRING }),
    ...(obj.Subject && { Subject: SENSITIVE_STRING }),
    ...(obj.Insights && { Insights: obj.Insights.map((item) => EmailInsightsFilterSensitiveLog(item)) }),
});
