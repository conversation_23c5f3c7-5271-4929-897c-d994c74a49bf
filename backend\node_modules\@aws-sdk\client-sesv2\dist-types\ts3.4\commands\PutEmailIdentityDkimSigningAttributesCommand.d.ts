import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutEmailIdentityDkimSigningAttributesRequest,
  PutEmailIdentityDkimSigningAttributesResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutEmailIdentityDkimSigningAttributesCommandInput
  extends PutEmailIdentityDkimSigningAttributesRequest {}
export interface PutEmailIdentityDkimSigningAttributesCommandOutput
  extends PutEmailIdentityDkimSigningAttributesResponse,
    __MetadataBearer {}
declare const PutEmailIdentityDkimSigningAttributesCommand_base: {
  new (
    input: PutEmailIdentityDkimSigningAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutEmailIdentityDkimSigningAttributesCommandInput,
    PutEmailIdentityDkimSigningAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutEmailIdentityDkimSigningAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutEmailIdentityDkimSigningAttributesCommandInput,
    PutEmailIdentityDkimSigningAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutEmailIdentityDkimSigningAttributesCommand extends PutEmailIdentityDkimSigningAttributesCommand_base {
  protected static __types: {
    api: {
      input: PutEmailIdentityDkimSigningAttributesRequest;
      output: PutEmailIdentityDkimSigningAttributesResponse;
    };
    sdk: {
      input: PutEmailIdentityDkimSigningAttributesCommandInput;
      output: PutEmailIdentityDkimSigningAttributesCommandOutput;
    };
  };
}
