import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  GetEmailIdentityRequest,
  GetEmailIdentityResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetEmailIdentityCommandInput extends GetEmailIdentityRequest {}
export interface GetEmailIdentityCommandOutput
  extends GetEmailIdentityResponse,
    __MetadataBearer {}
declare const GetEmailIdentityCommand_base: {
  new (
    input: GetEmailIdentityCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetEmailIdentityCommandInput,
    GetEmailIdentityCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetEmailIdentityCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetEmailIdentityCommandInput,
    GetEmailIdentityCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetEmailIdentityCommand extends GetEmailIdentityCommand_base {
  protected static __types: {
    api: {
      input: GetEmailIdentityRequest;
      output: GetEmailIdentityResponse;
    };
    sdk: {
      input: GetEmailIdentityCommandInput;
      output: GetEmailIdentityCommandOutput;
    };
  };
}
