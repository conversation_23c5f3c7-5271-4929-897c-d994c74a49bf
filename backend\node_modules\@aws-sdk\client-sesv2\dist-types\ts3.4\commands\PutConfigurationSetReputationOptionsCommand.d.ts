import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutConfigurationSetReputationOptionsRequest,
  PutConfigurationSetReputationOptionsResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutConfigurationSetReputationOptionsCommandInput
  extends PutConfigurationSetReputationOptionsRequest {}
export interface PutConfigurationSetReputationOptionsCommandOutput
  extends PutConfigurationSetReputationOptionsResponse,
    __MetadataBearer {}
declare const PutConfigurationSetReputationOptionsCommand_base: {
  new (
    input: PutConfigurationSetReputationOptionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutConfigurationSetReputationOptionsCommandInput,
    PutConfigurationSetReputationOptionsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutConfigurationSetReputationOptionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutConfigurationSetReputationOptionsCommandInput,
    PutConfigurationSetReputationOptionsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutConfigurationSetReputationOptionsCommand extends PutConfigurationSetReputationOptionsCommand_base {
  protected static __types: {
    api: {
      input: PutConfigurationSetReputationOptionsRequest;
      output: {};
    };
    sdk: {
      input: PutConfigurationSetReputationOptionsCommandInput;
      output: PutConfigurationSetReputationOptionsCommandOutput;
    };
  };
}
