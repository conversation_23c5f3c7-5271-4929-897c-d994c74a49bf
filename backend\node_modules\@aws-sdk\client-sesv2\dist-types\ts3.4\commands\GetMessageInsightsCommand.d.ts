import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetMessageInsightsRequest,
  GetMessageInsightsResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetMessageInsightsCommandInput
  extends GetMessageInsightsRequest {}
export interface GetMessageInsightsCommandOutput
  extends GetMessageInsightsResponse,
    __MetadataBearer {}
declare const GetMessageInsightsCommand_base: {
  new (
    input: GetMessageInsightsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetMessageInsightsCommandInput,
    GetMessageInsightsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetMessageInsightsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetMessageInsightsCommandInput,
    GetMessageInsightsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetMessageInsightsCommand extends GetMessageInsightsCommand_base {
  protected static __types: {
    api: {
      input: GetMessageInsightsRequest;
      output: GetMessageInsightsResponse;
    };
    sdk: {
      input: GetMessageInsightsCommandInput;
      output: GetMessageInsightsCommandOutput;
    };
  };
}
