import { Paginator } from "@smithy/types";
import { ListRecommendationsCommandInput, ListRecommendationsCommandOutput } from "../commands/ListRecommendationsCommand";
import { SESv2PaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListRecommendations: (config: SESv2PaginationConfiguration, input: ListRecommendationsCommandInput, ...rest: any[]) => Paginator<ListRecommendationsCommandOutput>;
