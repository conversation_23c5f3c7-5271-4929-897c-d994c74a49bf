import { HttpRequest as __HttpRequest, HttpResponse as __HttpResponse } from "@smithy/protocol-http";
import { SerdeContext as __SerdeContext } from "@smithy/types";
import { BatchGetMetricDataCommandInput, BatchGetMetricDataCommandOutput } from "../commands/BatchGetMetricDataCommand";
import { CancelExportJobCommandInput, CancelExportJobCommandOutput } from "../commands/CancelExportJobCommand";
import { CreateConfigurationSetCommandInput, CreateConfigurationSetCommandOutput } from "../commands/CreateConfigurationSetCommand";
import { CreateConfigurationSetEventDestinationCommandInput, CreateConfigurationSetEventDestinationCommandOutput } from "../commands/CreateConfigurationSetEventDestinationCommand";
import { CreateContactCommandInput, CreateContactCommandOutput } from "../commands/CreateContactCommand";
import { CreateContactListCommandInput, CreateContactListCommandOutput } from "../commands/CreateContactListCommand";
import { CreateCustomVerificationEmailTemplateCommandInput, CreateCustomVerificationEmailTemplateCommandOutput } from "../commands/CreateCustomVerificationEmailTemplateCommand";
import { CreateDedicatedIpPoolCommandInput, CreateDedicatedIpPoolCommandOutput } from "../commands/CreateDedicatedIpPoolCommand";
import { CreateDeliverabilityTestReportCommandInput, CreateDeliverabilityTestReportCommandOutput } from "../commands/CreateDeliverabilityTestReportCommand";
import { CreateEmailIdentityCommandInput, CreateEmailIdentityCommandOutput } from "../commands/CreateEmailIdentityCommand";
import { CreateEmailIdentityPolicyCommandInput, CreateEmailIdentityPolicyCommandOutput } from "../commands/CreateEmailIdentityPolicyCommand";
import { CreateEmailTemplateCommandInput, CreateEmailTemplateCommandOutput } from "../commands/CreateEmailTemplateCommand";
import { CreateExportJobCommandInput, CreateExportJobCommandOutput } from "../commands/CreateExportJobCommand";
import { CreateImportJobCommandInput, CreateImportJobCommandOutput } from "../commands/CreateImportJobCommand";
import { CreateMultiRegionEndpointCommandInput, CreateMultiRegionEndpointCommandOutput } from "../commands/CreateMultiRegionEndpointCommand";
import { CreateTenantCommandInput, CreateTenantCommandOutput } from "../commands/CreateTenantCommand";
import { CreateTenantResourceAssociationCommandInput, CreateTenantResourceAssociationCommandOutput } from "../commands/CreateTenantResourceAssociationCommand";
import { DeleteConfigurationSetCommandInput, DeleteConfigurationSetCommandOutput } from "../commands/DeleteConfigurationSetCommand";
import { DeleteConfigurationSetEventDestinationCommandInput, DeleteConfigurationSetEventDestinationCommandOutput } from "../commands/DeleteConfigurationSetEventDestinationCommand";
import { DeleteContactCommandInput, DeleteContactCommandOutput } from "../commands/DeleteContactCommand";
import { DeleteContactListCommandInput, DeleteContactListCommandOutput } from "../commands/DeleteContactListCommand";
import { DeleteCustomVerificationEmailTemplateCommandInput, DeleteCustomVerificationEmailTemplateCommandOutput } from "../commands/DeleteCustomVerificationEmailTemplateCommand";
import { DeleteDedicatedIpPoolCommandInput, DeleteDedicatedIpPoolCommandOutput } from "../commands/DeleteDedicatedIpPoolCommand";
import { DeleteEmailIdentityCommandInput, DeleteEmailIdentityCommandOutput } from "../commands/DeleteEmailIdentityCommand";
import { DeleteEmailIdentityPolicyCommandInput, DeleteEmailIdentityPolicyCommandOutput } from "../commands/DeleteEmailIdentityPolicyCommand";
import { DeleteEmailTemplateCommandInput, DeleteEmailTemplateCommandOutput } from "../commands/DeleteEmailTemplateCommand";
import { DeleteMultiRegionEndpointCommandInput, DeleteMultiRegionEndpointCommandOutput } from "../commands/DeleteMultiRegionEndpointCommand";
import { DeleteSuppressedDestinationCommandInput, DeleteSuppressedDestinationCommandOutput } from "../commands/DeleteSuppressedDestinationCommand";
import { DeleteTenantCommandInput, DeleteTenantCommandOutput } from "../commands/DeleteTenantCommand";
import { DeleteTenantResourceAssociationCommandInput, DeleteTenantResourceAssociationCommandOutput } from "../commands/DeleteTenantResourceAssociationCommand";
import { GetAccountCommandInput, GetAccountCommandOutput } from "../commands/GetAccountCommand";
import { GetBlacklistReportsCommandInput, GetBlacklistReportsCommandOutput } from "../commands/GetBlacklistReportsCommand";
import { GetConfigurationSetCommandInput, GetConfigurationSetCommandOutput } from "../commands/GetConfigurationSetCommand";
import { GetConfigurationSetEventDestinationsCommandInput, GetConfigurationSetEventDestinationsCommandOutput } from "../commands/GetConfigurationSetEventDestinationsCommand";
import { GetContactCommandInput, GetContactCommandOutput } from "../commands/GetContactCommand";
import { GetContactListCommandInput, GetContactListCommandOutput } from "../commands/GetContactListCommand";
import { GetCustomVerificationEmailTemplateCommandInput, GetCustomVerificationEmailTemplateCommandOutput } from "../commands/GetCustomVerificationEmailTemplateCommand";
import { GetDedicatedIpCommandInput, GetDedicatedIpCommandOutput } from "../commands/GetDedicatedIpCommand";
import { GetDedicatedIpPoolCommandInput, GetDedicatedIpPoolCommandOutput } from "../commands/GetDedicatedIpPoolCommand";
import { GetDedicatedIpsCommandInput, GetDedicatedIpsCommandOutput } from "../commands/GetDedicatedIpsCommand";
import { GetDeliverabilityDashboardOptionsCommandInput, GetDeliverabilityDashboardOptionsCommandOutput } from "../commands/GetDeliverabilityDashboardOptionsCommand";
import { GetDeliverabilityTestReportCommandInput, GetDeliverabilityTestReportCommandOutput } from "../commands/GetDeliverabilityTestReportCommand";
import { GetDomainDeliverabilityCampaignCommandInput, GetDomainDeliverabilityCampaignCommandOutput } from "../commands/GetDomainDeliverabilityCampaignCommand";
import { GetDomainStatisticsReportCommandInput, GetDomainStatisticsReportCommandOutput } from "../commands/GetDomainStatisticsReportCommand";
import { GetEmailIdentityCommandInput, GetEmailIdentityCommandOutput } from "../commands/GetEmailIdentityCommand";
import { GetEmailIdentityPoliciesCommandInput, GetEmailIdentityPoliciesCommandOutput } from "../commands/GetEmailIdentityPoliciesCommand";
import { GetEmailTemplateCommandInput, GetEmailTemplateCommandOutput } from "../commands/GetEmailTemplateCommand";
import { GetExportJobCommandInput, GetExportJobCommandOutput } from "../commands/GetExportJobCommand";
import { GetImportJobCommandInput, GetImportJobCommandOutput } from "../commands/GetImportJobCommand";
import { GetMessageInsightsCommandInput, GetMessageInsightsCommandOutput } from "../commands/GetMessageInsightsCommand";
import { GetMultiRegionEndpointCommandInput, GetMultiRegionEndpointCommandOutput } from "../commands/GetMultiRegionEndpointCommand";
import { GetReputationEntityCommandInput, GetReputationEntityCommandOutput } from "../commands/GetReputationEntityCommand";
import { GetSuppressedDestinationCommandInput, GetSuppressedDestinationCommandOutput } from "../commands/GetSuppressedDestinationCommand";
import { GetTenantCommandInput, GetTenantCommandOutput } from "../commands/GetTenantCommand";
import { ListConfigurationSetsCommandInput, ListConfigurationSetsCommandOutput } from "../commands/ListConfigurationSetsCommand";
import { ListContactListsCommandInput, ListContactListsCommandOutput } from "../commands/ListContactListsCommand";
import { ListContactsCommandInput, ListContactsCommandOutput } from "../commands/ListContactsCommand";
import { ListCustomVerificationEmailTemplatesCommandInput, ListCustomVerificationEmailTemplatesCommandOutput } from "../commands/ListCustomVerificationEmailTemplatesCommand";
import { ListDedicatedIpPoolsCommandInput, ListDedicatedIpPoolsCommandOutput } from "../commands/ListDedicatedIpPoolsCommand";
import { ListDeliverabilityTestReportsCommandInput, ListDeliverabilityTestReportsCommandOutput } from "../commands/ListDeliverabilityTestReportsCommand";
import { ListDomainDeliverabilityCampaignsCommandInput, ListDomainDeliverabilityCampaignsCommandOutput } from "../commands/ListDomainDeliverabilityCampaignsCommand";
import { ListEmailIdentitiesCommandInput, ListEmailIdentitiesCommandOutput } from "../commands/ListEmailIdentitiesCommand";
import { ListEmailTemplatesCommandInput, ListEmailTemplatesCommandOutput } from "../commands/ListEmailTemplatesCommand";
import { ListExportJobsCommandInput, ListExportJobsCommandOutput } from "../commands/ListExportJobsCommand";
import { ListImportJobsCommandInput, ListImportJobsCommandOutput } from "../commands/ListImportJobsCommand";
import { ListMultiRegionEndpointsCommandInput, ListMultiRegionEndpointsCommandOutput } from "../commands/ListMultiRegionEndpointsCommand";
import { ListRecommendationsCommandInput, ListRecommendationsCommandOutput } from "../commands/ListRecommendationsCommand";
import { ListReputationEntitiesCommandInput, ListReputationEntitiesCommandOutput } from "../commands/ListReputationEntitiesCommand";
import { ListResourceTenantsCommandInput, ListResourceTenantsCommandOutput } from "../commands/ListResourceTenantsCommand";
import { ListSuppressedDestinationsCommandInput, ListSuppressedDestinationsCommandOutput } from "../commands/ListSuppressedDestinationsCommand";
import { ListTagsForResourceCommandInput, ListTagsForResourceCommandOutput } from "../commands/ListTagsForResourceCommand";
import { ListTenantResourcesCommandInput, ListTenantResourcesCommandOutput } from "../commands/ListTenantResourcesCommand";
import { ListTenantsCommandInput, ListTenantsCommandOutput } from "../commands/ListTenantsCommand";
import { PutAccountDedicatedIpWarmupAttributesCommandInput, PutAccountDedicatedIpWarmupAttributesCommandOutput } from "../commands/PutAccountDedicatedIpWarmupAttributesCommand";
import { PutAccountDetailsCommandInput, PutAccountDetailsCommandOutput } from "../commands/PutAccountDetailsCommand";
import { PutAccountSendingAttributesCommandInput, PutAccountSendingAttributesCommandOutput } from "../commands/PutAccountSendingAttributesCommand";
import { PutAccountSuppressionAttributesCommandInput, PutAccountSuppressionAttributesCommandOutput } from "../commands/PutAccountSuppressionAttributesCommand";
import { PutAccountVdmAttributesCommandInput, PutAccountVdmAttributesCommandOutput } from "../commands/PutAccountVdmAttributesCommand";
import { PutConfigurationSetArchivingOptionsCommandInput, PutConfigurationSetArchivingOptionsCommandOutput } from "../commands/PutConfigurationSetArchivingOptionsCommand";
import { PutConfigurationSetDeliveryOptionsCommandInput, PutConfigurationSetDeliveryOptionsCommandOutput } from "../commands/PutConfigurationSetDeliveryOptionsCommand";
import { PutConfigurationSetReputationOptionsCommandInput, PutConfigurationSetReputationOptionsCommandOutput } from "../commands/PutConfigurationSetReputationOptionsCommand";
import { PutConfigurationSetSendingOptionsCommandInput, PutConfigurationSetSendingOptionsCommandOutput } from "../commands/PutConfigurationSetSendingOptionsCommand";
import { PutConfigurationSetSuppressionOptionsCommandInput, PutConfigurationSetSuppressionOptionsCommandOutput } from "../commands/PutConfigurationSetSuppressionOptionsCommand";
import { PutConfigurationSetTrackingOptionsCommandInput, PutConfigurationSetTrackingOptionsCommandOutput } from "../commands/PutConfigurationSetTrackingOptionsCommand";
import { PutConfigurationSetVdmOptionsCommandInput, PutConfigurationSetVdmOptionsCommandOutput } from "../commands/PutConfigurationSetVdmOptionsCommand";
import { PutDedicatedIpInPoolCommandInput, PutDedicatedIpInPoolCommandOutput } from "../commands/PutDedicatedIpInPoolCommand";
import { PutDedicatedIpPoolScalingAttributesCommandInput, PutDedicatedIpPoolScalingAttributesCommandOutput } from "../commands/PutDedicatedIpPoolScalingAttributesCommand";
import { PutDedicatedIpWarmupAttributesCommandInput, PutDedicatedIpWarmupAttributesCommandOutput } from "../commands/PutDedicatedIpWarmupAttributesCommand";
import { PutDeliverabilityDashboardOptionCommandInput, PutDeliverabilityDashboardOptionCommandOutput } from "../commands/PutDeliverabilityDashboardOptionCommand";
import { PutEmailIdentityConfigurationSetAttributesCommandInput, PutEmailIdentityConfigurationSetAttributesCommandOutput } from "../commands/PutEmailIdentityConfigurationSetAttributesCommand";
import { PutEmailIdentityDkimAttributesCommandInput, PutEmailIdentityDkimAttributesCommandOutput } from "../commands/PutEmailIdentityDkimAttributesCommand";
import { PutEmailIdentityDkimSigningAttributesCommandInput, PutEmailIdentityDkimSigningAttributesCommandOutput } from "../commands/PutEmailIdentityDkimSigningAttributesCommand";
import { PutEmailIdentityFeedbackAttributesCommandInput, PutEmailIdentityFeedbackAttributesCommandOutput } from "../commands/PutEmailIdentityFeedbackAttributesCommand";
import { PutEmailIdentityMailFromAttributesCommandInput, PutEmailIdentityMailFromAttributesCommandOutput } from "../commands/PutEmailIdentityMailFromAttributesCommand";
import { PutSuppressedDestinationCommandInput, PutSuppressedDestinationCommandOutput } from "../commands/PutSuppressedDestinationCommand";
import { SendBulkEmailCommandInput, SendBulkEmailCommandOutput } from "../commands/SendBulkEmailCommand";
import { SendCustomVerificationEmailCommandInput, SendCustomVerificationEmailCommandOutput } from "../commands/SendCustomVerificationEmailCommand";
import { SendEmailCommandInput, SendEmailCommandOutput } from "../commands/SendEmailCommand";
import { TagResourceCommandInput, TagResourceCommandOutput } from "../commands/TagResourceCommand";
import { TestRenderEmailTemplateCommandInput, TestRenderEmailTemplateCommandOutput } from "../commands/TestRenderEmailTemplateCommand";
import { UntagResourceCommandInput, UntagResourceCommandOutput } from "../commands/UntagResourceCommand";
import { UpdateConfigurationSetEventDestinationCommandInput, UpdateConfigurationSetEventDestinationCommandOutput } from "../commands/UpdateConfigurationSetEventDestinationCommand";
import { UpdateContactCommandInput, UpdateContactCommandOutput } from "../commands/UpdateContactCommand";
import { UpdateContactListCommandInput, UpdateContactListCommandOutput } from "../commands/UpdateContactListCommand";
import { UpdateCustomVerificationEmailTemplateCommandInput, UpdateCustomVerificationEmailTemplateCommandOutput } from "../commands/UpdateCustomVerificationEmailTemplateCommand";
import { UpdateEmailIdentityPolicyCommandInput, UpdateEmailIdentityPolicyCommandOutput } from "../commands/UpdateEmailIdentityPolicyCommand";
import { UpdateEmailTemplateCommandInput, UpdateEmailTemplateCommandOutput } from "../commands/UpdateEmailTemplateCommand";
import { UpdateReputationEntityCustomerManagedStatusCommandInput, UpdateReputationEntityCustomerManagedStatusCommandOutput } from "../commands/UpdateReputationEntityCustomerManagedStatusCommand";
import { UpdateReputationEntityPolicyCommandInput, UpdateReputationEntityPolicyCommandOutput } from "../commands/UpdateReputationEntityPolicyCommand";
/**
 * serializeAws_restJson1BatchGetMetricDataCommand
 */
export declare const se_BatchGetMetricDataCommand: (input: BatchGetMetricDataCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CancelExportJobCommand
 */
export declare const se_CancelExportJobCommand: (input: CancelExportJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateConfigurationSetCommand
 */
export declare const se_CreateConfigurationSetCommand: (input: CreateConfigurationSetCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateConfigurationSetEventDestinationCommand
 */
export declare const se_CreateConfigurationSetEventDestinationCommand: (input: CreateConfigurationSetEventDestinationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateContactCommand
 */
export declare const se_CreateContactCommand: (input: CreateContactCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateContactListCommand
 */
export declare const se_CreateContactListCommand: (input: CreateContactListCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateCustomVerificationEmailTemplateCommand
 */
export declare const se_CreateCustomVerificationEmailTemplateCommand: (input: CreateCustomVerificationEmailTemplateCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateDedicatedIpPoolCommand
 */
export declare const se_CreateDedicatedIpPoolCommand: (input: CreateDedicatedIpPoolCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateDeliverabilityTestReportCommand
 */
export declare const se_CreateDeliverabilityTestReportCommand: (input: CreateDeliverabilityTestReportCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateEmailIdentityCommand
 */
export declare const se_CreateEmailIdentityCommand: (input: CreateEmailIdentityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateEmailIdentityPolicyCommand
 */
export declare const se_CreateEmailIdentityPolicyCommand: (input: CreateEmailIdentityPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateEmailTemplateCommand
 */
export declare const se_CreateEmailTemplateCommand: (input: CreateEmailTemplateCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateExportJobCommand
 */
export declare const se_CreateExportJobCommand: (input: CreateExportJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateImportJobCommand
 */
export declare const se_CreateImportJobCommand: (input: CreateImportJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateMultiRegionEndpointCommand
 */
export declare const se_CreateMultiRegionEndpointCommand: (input: CreateMultiRegionEndpointCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateTenantCommand
 */
export declare const se_CreateTenantCommand: (input: CreateTenantCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateTenantResourceAssociationCommand
 */
export declare const se_CreateTenantResourceAssociationCommand: (input: CreateTenantResourceAssociationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteConfigurationSetCommand
 */
export declare const se_DeleteConfigurationSetCommand: (input: DeleteConfigurationSetCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteConfigurationSetEventDestinationCommand
 */
export declare const se_DeleteConfigurationSetEventDestinationCommand: (input: DeleteConfigurationSetEventDestinationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteContactCommand
 */
export declare const se_DeleteContactCommand: (input: DeleteContactCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteContactListCommand
 */
export declare const se_DeleteContactListCommand: (input: DeleteContactListCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteCustomVerificationEmailTemplateCommand
 */
export declare const se_DeleteCustomVerificationEmailTemplateCommand: (input: DeleteCustomVerificationEmailTemplateCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteDedicatedIpPoolCommand
 */
export declare const se_DeleteDedicatedIpPoolCommand: (input: DeleteDedicatedIpPoolCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteEmailIdentityCommand
 */
export declare const se_DeleteEmailIdentityCommand: (input: DeleteEmailIdentityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteEmailIdentityPolicyCommand
 */
export declare const se_DeleteEmailIdentityPolicyCommand: (input: DeleteEmailIdentityPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteEmailTemplateCommand
 */
export declare const se_DeleteEmailTemplateCommand: (input: DeleteEmailTemplateCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteMultiRegionEndpointCommand
 */
export declare const se_DeleteMultiRegionEndpointCommand: (input: DeleteMultiRegionEndpointCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteSuppressedDestinationCommand
 */
export declare const se_DeleteSuppressedDestinationCommand: (input: DeleteSuppressedDestinationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteTenantCommand
 */
export declare const se_DeleteTenantCommand: (input: DeleteTenantCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteTenantResourceAssociationCommand
 */
export declare const se_DeleteTenantResourceAssociationCommand: (input: DeleteTenantResourceAssociationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetAccountCommand
 */
export declare const se_GetAccountCommand: (input: GetAccountCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetBlacklistReportsCommand
 */
export declare const se_GetBlacklistReportsCommand: (input: GetBlacklistReportsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetConfigurationSetCommand
 */
export declare const se_GetConfigurationSetCommand: (input: GetConfigurationSetCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetConfigurationSetEventDestinationsCommand
 */
export declare const se_GetConfigurationSetEventDestinationsCommand: (input: GetConfigurationSetEventDestinationsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetContactCommand
 */
export declare const se_GetContactCommand: (input: GetContactCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetContactListCommand
 */
export declare const se_GetContactListCommand: (input: GetContactListCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetCustomVerificationEmailTemplateCommand
 */
export declare const se_GetCustomVerificationEmailTemplateCommand: (input: GetCustomVerificationEmailTemplateCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetDedicatedIpCommand
 */
export declare const se_GetDedicatedIpCommand: (input: GetDedicatedIpCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetDedicatedIpPoolCommand
 */
export declare const se_GetDedicatedIpPoolCommand: (input: GetDedicatedIpPoolCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetDedicatedIpsCommand
 */
export declare const se_GetDedicatedIpsCommand: (input: GetDedicatedIpsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetDeliverabilityDashboardOptionsCommand
 */
export declare const se_GetDeliverabilityDashboardOptionsCommand: (input: GetDeliverabilityDashboardOptionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetDeliverabilityTestReportCommand
 */
export declare const se_GetDeliverabilityTestReportCommand: (input: GetDeliverabilityTestReportCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetDomainDeliverabilityCampaignCommand
 */
export declare const se_GetDomainDeliverabilityCampaignCommand: (input: GetDomainDeliverabilityCampaignCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetDomainStatisticsReportCommand
 */
export declare const se_GetDomainStatisticsReportCommand: (input: GetDomainStatisticsReportCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetEmailIdentityCommand
 */
export declare const se_GetEmailIdentityCommand: (input: GetEmailIdentityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetEmailIdentityPoliciesCommand
 */
export declare const se_GetEmailIdentityPoliciesCommand: (input: GetEmailIdentityPoliciesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetEmailTemplateCommand
 */
export declare const se_GetEmailTemplateCommand: (input: GetEmailTemplateCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetExportJobCommand
 */
export declare const se_GetExportJobCommand: (input: GetExportJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetImportJobCommand
 */
export declare const se_GetImportJobCommand: (input: GetImportJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetMessageInsightsCommand
 */
export declare const se_GetMessageInsightsCommand: (input: GetMessageInsightsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetMultiRegionEndpointCommand
 */
export declare const se_GetMultiRegionEndpointCommand: (input: GetMultiRegionEndpointCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetReputationEntityCommand
 */
export declare const se_GetReputationEntityCommand: (input: GetReputationEntityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetSuppressedDestinationCommand
 */
export declare const se_GetSuppressedDestinationCommand: (input: GetSuppressedDestinationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetTenantCommand
 */
export declare const se_GetTenantCommand: (input: GetTenantCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListConfigurationSetsCommand
 */
export declare const se_ListConfigurationSetsCommand: (input: ListConfigurationSetsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListContactListsCommand
 */
export declare const se_ListContactListsCommand: (input: ListContactListsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListContactsCommand
 */
export declare const se_ListContactsCommand: (input: ListContactsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListCustomVerificationEmailTemplatesCommand
 */
export declare const se_ListCustomVerificationEmailTemplatesCommand: (input: ListCustomVerificationEmailTemplatesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListDedicatedIpPoolsCommand
 */
export declare const se_ListDedicatedIpPoolsCommand: (input: ListDedicatedIpPoolsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListDeliverabilityTestReportsCommand
 */
export declare const se_ListDeliverabilityTestReportsCommand: (input: ListDeliverabilityTestReportsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListDomainDeliverabilityCampaignsCommand
 */
export declare const se_ListDomainDeliverabilityCampaignsCommand: (input: ListDomainDeliverabilityCampaignsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListEmailIdentitiesCommand
 */
export declare const se_ListEmailIdentitiesCommand: (input: ListEmailIdentitiesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListEmailTemplatesCommand
 */
export declare const se_ListEmailTemplatesCommand: (input: ListEmailTemplatesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListExportJobsCommand
 */
export declare const se_ListExportJobsCommand: (input: ListExportJobsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListImportJobsCommand
 */
export declare const se_ListImportJobsCommand: (input: ListImportJobsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListMultiRegionEndpointsCommand
 */
export declare const se_ListMultiRegionEndpointsCommand: (input: ListMultiRegionEndpointsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListRecommendationsCommand
 */
export declare const se_ListRecommendationsCommand: (input: ListRecommendationsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListReputationEntitiesCommand
 */
export declare const se_ListReputationEntitiesCommand: (input: ListReputationEntitiesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListResourceTenantsCommand
 */
export declare const se_ListResourceTenantsCommand: (input: ListResourceTenantsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListSuppressedDestinationsCommand
 */
export declare const se_ListSuppressedDestinationsCommand: (input: ListSuppressedDestinationsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListTagsForResourceCommand
 */
export declare const se_ListTagsForResourceCommand: (input: ListTagsForResourceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListTenantResourcesCommand
 */
export declare const se_ListTenantResourcesCommand: (input: ListTenantResourcesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListTenantsCommand
 */
export declare const se_ListTenantsCommand: (input: ListTenantsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutAccountDedicatedIpWarmupAttributesCommand
 */
export declare const se_PutAccountDedicatedIpWarmupAttributesCommand: (input: PutAccountDedicatedIpWarmupAttributesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutAccountDetailsCommand
 */
export declare const se_PutAccountDetailsCommand: (input: PutAccountDetailsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutAccountSendingAttributesCommand
 */
export declare const se_PutAccountSendingAttributesCommand: (input: PutAccountSendingAttributesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutAccountSuppressionAttributesCommand
 */
export declare const se_PutAccountSuppressionAttributesCommand: (input: PutAccountSuppressionAttributesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutAccountVdmAttributesCommand
 */
export declare const se_PutAccountVdmAttributesCommand: (input: PutAccountVdmAttributesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutConfigurationSetArchivingOptionsCommand
 */
export declare const se_PutConfigurationSetArchivingOptionsCommand: (input: PutConfigurationSetArchivingOptionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutConfigurationSetDeliveryOptionsCommand
 */
export declare const se_PutConfigurationSetDeliveryOptionsCommand: (input: PutConfigurationSetDeliveryOptionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutConfigurationSetReputationOptionsCommand
 */
export declare const se_PutConfigurationSetReputationOptionsCommand: (input: PutConfigurationSetReputationOptionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutConfigurationSetSendingOptionsCommand
 */
export declare const se_PutConfigurationSetSendingOptionsCommand: (input: PutConfigurationSetSendingOptionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutConfigurationSetSuppressionOptionsCommand
 */
export declare const se_PutConfigurationSetSuppressionOptionsCommand: (input: PutConfigurationSetSuppressionOptionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutConfigurationSetTrackingOptionsCommand
 */
export declare const se_PutConfigurationSetTrackingOptionsCommand: (input: PutConfigurationSetTrackingOptionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutConfigurationSetVdmOptionsCommand
 */
export declare const se_PutConfigurationSetVdmOptionsCommand: (input: PutConfigurationSetVdmOptionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutDedicatedIpInPoolCommand
 */
export declare const se_PutDedicatedIpInPoolCommand: (input: PutDedicatedIpInPoolCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutDedicatedIpPoolScalingAttributesCommand
 */
export declare const se_PutDedicatedIpPoolScalingAttributesCommand: (input: PutDedicatedIpPoolScalingAttributesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutDedicatedIpWarmupAttributesCommand
 */
export declare const se_PutDedicatedIpWarmupAttributesCommand: (input: PutDedicatedIpWarmupAttributesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutDeliverabilityDashboardOptionCommand
 */
export declare const se_PutDeliverabilityDashboardOptionCommand: (input: PutDeliverabilityDashboardOptionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutEmailIdentityConfigurationSetAttributesCommand
 */
export declare const se_PutEmailIdentityConfigurationSetAttributesCommand: (input: PutEmailIdentityConfigurationSetAttributesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutEmailIdentityDkimAttributesCommand
 */
export declare const se_PutEmailIdentityDkimAttributesCommand: (input: PutEmailIdentityDkimAttributesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutEmailIdentityDkimSigningAttributesCommand
 */
export declare const se_PutEmailIdentityDkimSigningAttributesCommand: (input: PutEmailIdentityDkimSigningAttributesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutEmailIdentityFeedbackAttributesCommand
 */
export declare const se_PutEmailIdentityFeedbackAttributesCommand: (input: PutEmailIdentityFeedbackAttributesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutEmailIdentityMailFromAttributesCommand
 */
export declare const se_PutEmailIdentityMailFromAttributesCommand: (input: PutEmailIdentityMailFromAttributesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutSuppressedDestinationCommand
 */
export declare const se_PutSuppressedDestinationCommand: (input: PutSuppressedDestinationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1SendBulkEmailCommand
 */
export declare const se_SendBulkEmailCommand: (input: SendBulkEmailCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1SendCustomVerificationEmailCommand
 */
export declare const se_SendCustomVerificationEmailCommand: (input: SendCustomVerificationEmailCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1SendEmailCommand
 */
export declare const se_SendEmailCommand: (input: SendEmailCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1TagResourceCommand
 */
export declare const se_TagResourceCommand: (input: TagResourceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1TestRenderEmailTemplateCommand
 */
export declare const se_TestRenderEmailTemplateCommand: (input: TestRenderEmailTemplateCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UntagResourceCommand
 */
export declare const se_UntagResourceCommand: (input: UntagResourceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateConfigurationSetEventDestinationCommand
 */
export declare const se_UpdateConfigurationSetEventDestinationCommand: (input: UpdateConfigurationSetEventDestinationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateContactCommand
 */
export declare const se_UpdateContactCommand: (input: UpdateContactCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateContactListCommand
 */
export declare const se_UpdateContactListCommand: (input: UpdateContactListCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateCustomVerificationEmailTemplateCommand
 */
export declare const se_UpdateCustomVerificationEmailTemplateCommand: (input: UpdateCustomVerificationEmailTemplateCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateEmailIdentityPolicyCommand
 */
export declare const se_UpdateEmailIdentityPolicyCommand: (input: UpdateEmailIdentityPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateEmailTemplateCommand
 */
export declare const se_UpdateEmailTemplateCommand: (input: UpdateEmailTemplateCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateReputationEntityCustomerManagedStatusCommand
 */
export declare const se_UpdateReputationEntityCustomerManagedStatusCommand: (input: UpdateReputationEntityCustomerManagedStatusCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateReputationEntityPolicyCommand
 */
export declare const se_UpdateReputationEntityPolicyCommand: (input: UpdateReputationEntityPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * deserializeAws_restJson1BatchGetMetricDataCommand
 */
export declare const de_BatchGetMetricDataCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<BatchGetMetricDataCommandOutput>;
/**
 * deserializeAws_restJson1CancelExportJobCommand
 */
export declare const de_CancelExportJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CancelExportJobCommandOutput>;
/**
 * deserializeAws_restJson1CreateConfigurationSetCommand
 */
export declare const de_CreateConfigurationSetCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateConfigurationSetCommandOutput>;
/**
 * deserializeAws_restJson1CreateConfigurationSetEventDestinationCommand
 */
export declare const de_CreateConfigurationSetEventDestinationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateConfigurationSetEventDestinationCommandOutput>;
/**
 * deserializeAws_restJson1CreateContactCommand
 */
export declare const de_CreateContactCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateContactCommandOutput>;
/**
 * deserializeAws_restJson1CreateContactListCommand
 */
export declare const de_CreateContactListCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateContactListCommandOutput>;
/**
 * deserializeAws_restJson1CreateCustomVerificationEmailTemplateCommand
 */
export declare const de_CreateCustomVerificationEmailTemplateCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateCustomVerificationEmailTemplateCommandOutput>;
/**
 * deserializeAws_restJson1CreateDedicatedIpPoolCommand
 */
export declare const de_CreateDedicatedIpPoolCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateDedicatedIpPoolCommandOutput>;
/**
 * deserializeAws_restJson1CreateDeliverabilityTestReportCommand
 */
export declare const de_CreateDeliverabilityTestReportCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateDeliverabilityTestReportCommandOutput>;
/**
 * deserializeAws_restJson1CreateEmailIdentityCommand
 */
export declare const de_CreateEmailIdentityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateEmailIdentityCommandOutput>;
/**
 * deserializeAws_restJson1CreateEmailIdentityPolicyCommand
 */
export declare const de_CreateEmailIdentityPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateEmailIdentityPolicyCommandOutput>;
/**
 * deserializeAws_restJson1CreateEmailTemplateCommand
 */
export declare const de_CreateEmailTemplateCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateEmailTemplateCommandOutput>;
/**
 * deserializeAws_restJson1CreateExportJobCommand
 */
export declare const de_CreateExportJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateExportJobCommandOutput>;
/**
 * deserializeAws_restJson1CreateImportJobCommand
 */
export declare const de_CreateImportJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateImportJobCommandOutput>;
/**
 * deserializeAws_restJson1CreateMultiRegionEndpointCommand
 */
export declare const de_CreateMultiRegionEndpointCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateMultiRegionEndpointCommandOutput>;
/**
 * deserializeAws_restJson1CreateTenantCommand
 */
export declare const de_CreateTenantCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateTenantCommandOutput>;
/**
 * deserializeAws_restJson1CreateTenantResourceAssociationCommand
 */
export declare const de_CreateTenantResourceAssociationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateTenantResourceAssociationCommandOutput>;
/**
 * deserializeAws_restJson1DeleteConfigurationSetCommand
 */
export declare const de_DeleteConfigurationSetCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteConfigurationSetCommandOutput>;
/**
 * deserializeAws_restJson1DeleteConfigurationSetEventDestinationCommand
 */
export declare const de_DeleteConfigurationSetEventDestinationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteConfigurationSetEventDestinationCommandOutput>;
/**
 * deserializeAws_restJson1DeleteContactCommand
 */
export declare const de_DeleteContactCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteContactCommandOutput>;
/**
 * deserializeAws_restJson1DeleteContactListCommand
 */
export declare const de_DeleteContactListCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteContactListCommandOutput>;
/**
 * deserializeAws_restJson1DeleteCustomVerificationEmailTemplateCommand
 */
export declare const de_DeleteCustomVerificationEmailTemplateCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteCustomVerificationEmailTemplateCommandOutput>;
/**
 * deserializeAws_restJson1DeleteDedicatedIpPoolCommand
 */
export declare const de_DeleteDedicatedIpPoolCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteDedicatedIpPoolCommandOutput>;
/**
 * deserializeAws_restJson1DeleteEmailIdentityCommand
 */
export declare const de_DeleteEmailIdentityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteEmailIdentityCommandOutput>;
/**
 * deserializeAws_restJson1DeleteEmailIdentityPolicyCommand
 */
export declare const de_DeleteEmailIdentityPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteEmailIdentityPolicyCommandOutput>;
/**
 * deserializeAws_restJson1DeleteEmailTemplateCommand
 */
export declare const de_DeleteEmailTemplateCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteEmailTemplateCommandOutput>;
/**
 * deserializeAws_restJson1DeleteMultiRegionEndpointCommand
 */
export declare const de_DeleteMultiRegionEndpointCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteMultiRegionEndpointCommandOutput>;
/**
 * deserializeAws_restJson1DeleteSuppressedDestinationCommand
 */
export declare const de_DeleteSuppressedDestinationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteSuppressedDestinationCommandOutput>;
/**
 * deserializeAws_restJson1DeleteTenantCommand
 */
export declare const de_DeleteTenantCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteTenantCommandOutput>;
/**
 * deserializeAws_restJson1DeleteTenantResourceAssociationCommand
 */
export declare const de_DeleteTenantResourceAssociationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteTenantResourceAssociationCommandOutput>;
/**
 * deserializeAws_restJson1GetAccountCommand
 */
export declare const de_GetAccountCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetAccountCommandOutput>;
/**
 * deserializeAws_restJson1GetBlacklistReportsCommand
 */
export declare const de_GetBlacklistReportsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBlacklistReportsCommandOutput>;
/**
 * deserializeAws_restJson1GetConfigurationSetCommand
 */
export declare const de_GetConfigurationSetCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetConfigurationSetCommandOutput>;
/**
 * deserializeAws_restJson1GetConfigurationSetEventDestinationsCommand
 */
export declare const de_GetConfigurationSetEventDestinationsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetConfigurationSetEventDestinationsCommandOutput>;
/**
 * deserializeAws_restJson1GetContactCommand
 */
export declare const de_GetContactCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetContactCommandOutput>;
/**
 * deserializeAws_restJson1GetContactListCommand
 */
export declare const de_GetContactListCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetContactListCommandOutput>;
/**
 * deserializeAws_restJson1GetCustomVerificationEmailTemplateCommand
 */
export declare const de_GetCustomVerificationEmailTemplateCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetCustomVerificationEmailTemplateCommandOutput>;
/**
 * deserializeAws_restJson1GetDedicatedIpCommand
 */
export declare const de_GetDedicatedIpCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetDedicatedIpCommandOutput>;
/**
 * deserializeAws_restJson1GetDedicatedIpPoolCommand
 */
export declare const de_GetDedicatedIpPoolCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetDedicatedIpPoolCommandOutput>;
/**
 * deserializeAws_restJson1GetDedicatedIpsCommand
 */
export declare const de_GetDedicatedIpsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetDedicatedIpsCommandOutput>;
/**
 * deserializeAws_restJson1GetDeliverabilityDashboardOptionsCommand
 */
export declare const de_GetDeliverabilityDashboardOptionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetDeliverabilityDashboardOptionsCommandOutput>;
/**
 * deserializeAws_restJson1GetDeliverabilityTestReportCommand
 */
export declare const de_GetDeliverabilityTestReportCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetDeliverabilityTestReportCommandOutput>;
/**
 * deserializeAws_restJson1GetDomainDeliverabilityCampaignCommand
 */
export declare const de_GetDomainDeliverabilityCampaignCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetDomainDeliverabilityCampaignCommandOutput>;
/**
 * deserializeAws_restJson1GetDomainStatisticsReportCommand
 */
export declare const de_GetDomainStatisticsReportCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetDomainStatisticsReportCommandOutput>;
/**
 * deserializeAws_restJson1GetEmailIdentityCommand
 */
export declare const de_GetEmailIdentityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetEmailIdentityCommandOutput>;
/**
 * deserializeAws_restJson1GetEmailIdentityPoliciesCommand
 */
export declare const de_GetEmailIdentityPoliciesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetEmailIdentityPoliciesCommandOutput>;
/**
 * deserializeAws_restJson1GetEmailTemplateCommand
 */
export declare const de_GetEmailTemplateCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetEmailTemplateCommandOutput>;
/**
 * deserializeAws_restJson1GetExportJobCommand
 */
export declare const de_GetExportJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetExportJobCommandOutput>;
/**
 * deserializeAws_restJson1GetImportJobCommand
 */
export declare const de_GetImportJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetImportJobCommandOutput>;
/**
 * deserializeAws_restJson1GetMessageInsightsCommand
 */
export declare const de_GetMessageInsightsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetMessageInsightsCommandOutput>;
/**
 * deserializeAws_restJson1GetMultiRegionEndpointCommand
 */
export declare const de_GetMultiRegionEndpointCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetMultiRegionEndpointCommandOutput>;
/**
 * deserializeAws_restJson1GetReputationEntityCommand
 */
export declare const de_GetReputationEntityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetReputationEntityCommandOutput>;
/**
 * deserializeAws_restJson1GetSuppressedDestinationCommand
 */
export declare const de_GetSuppressedDestinationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetSuppressedDestinationCommandOutput>;
/**
 * deserializeAws_restJson1GetTenantCommand
 */
export declare const de_GetTenantCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetTenantCommandOutput>;
/**
 * deserializeAws_restJson1ListConfigurationSetsCommand
 */
export declare const de_ListConfigurationSetsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListConfigurationSetsCommandOutput>;
/**
 * deserializeAws_restJson1ListContactListsCommand
 */
export declare const de_ListContactListsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListContactListsCommandOutput>;
/**
 * deserializeAws_restJson1ListContactsCommand
 */
export declare const de_ListContactsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListContactsCommandOutput>;
/**
 * deserializeAws_restJson1ListCustomVerificationEmailTemplatesCommand
 */
export declare const de_ListCustomVerificationEmailTemplatesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListCustomVerificationEmailTemplatesCommandOutput>;
/**
 * deserializeAws_restJson1ListDedicatedIpPoolsCommand
 */
export declare const de_ListDedicatedIpPoolsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDedicatedIpPoolsCommandOutput>;
/**
 * deserializeAws_restJson1ListDeliverabilityTestReportsCommand
 */
export declare const de_ListDeliverabilityTestReportsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDeliverabilityTestReportsCommandOutput>;
/**
 * deserializeAws_restJson1ListDomainDeliverabilityCampaignsCommand
 */
export declare const de_ListDomainDeliverabilityCampaignsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDomainDeliverabilityCampaignsCommandOutput>;
/**
 * deserializeAws_restJson1ListEmailIdentitiesCommand
 */
export declare const de_ListEmailIdentitiesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListEmailIdentitiesCommandOutput>;
/**
 * deserializeAws_restJson1ListEmailTemplatesCommand
 */
export declare const de_ListEmailTemplatesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListEmailTemplatesCommandOutput>;
/**
 * deserializeAws_restJson1ListExportJobsCommand
 */
export declare const de_ListExportJobsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListExportJobsCommandOutput>;
/**
 * deserializeAws_restJson1ListImportJobsCommand
 */
export declare const de_ListImportJobsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListImportJobsCommandOutput>;
/**
 * deserializeAws_restJson1ListMultiRegionEndpointsCommand
 */
export declare const de_ListMultiRegionEndpointsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListMultiRegionEndpointsCommandOutput>;
/**
 * deserializeAws_restJson1ListRecommendationsCommand
 */
export declare const de_ListRecommendationsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListRecommendationsCommandOutput>;
/**
 * deserializeAws_restJson1ListReputationEntitiesCommand
 */
export declare const de_ListReputationEntitiesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListReputationEntitiesCommandOutput>;
/**
 * deserializeAws_restJson1ListResourceTenantsCommand
 */
export declare const de_ListResourceTenantsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListResourceTenantsCommandOutput>;
/**
 * deserializeAws_restJson1ListSuppressedDestinationsCommand
 */
export declare const de_ListSuppressedDestinationsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListSuppressedDestinationsCommandOutput>;
/**
 * deserializeAws_restJson1ListTagsForResourceCommand
 */
export declare const de_ListTagsForResourceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListTagsForResourceCommandOutput>;
/**
 * deserializeAws_restJson1ListTenantResourcesCommand
 */
export declare const de_ListTenantResourcesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListTenantResourcesCommandOutput>;
/**
 * deserializeAws_restJson1ListTenantsCommand
 */
export declare const de_ListTenantsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListTenantsCommandOutput>;
/**
 * deserializeAws_restJson1PutAccountDedicatedIpWarmupAttributesCommand
 */
export declare const de_PutAccountDedicatedIpWarmupAttributesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutAccountDedicatedIpWarmupAttributesCommandOutput>;
/**
 * deserializeAws_restJson1PutAccountDetailsCommand
 */
export declare const de_PutAccountDetailsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutAccountDetailsCommandOutput>;
/**
 * deserializeAws_restJson1PutAccountSendingAttributesCommand
 */
export declare const de_PutAccountSendingAttributesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutAccountSendingAttributesCommandOutput>;
/**
 * deserializeAws_restJson1PutAccountSuppressionAttributesCommand
 */
export declare const de_PutAccountSuppressionAttributesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutAccountSuppressionAttributesCommandOutput>;
/**
 * deserializeAws_restJson1PutAccountVdmAttributesCommand
 */
export declare const de_PutAccountVdmAttributesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutAccountVdmAttributesCommandOutput>;
/**
 * deserializeAws_restJson1PutConfigurationSetArchivingOptionsCommand
 */
export declare const de_PutConfigurationSetArchivingOptionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutConfigurationSetArchivingOptionsCommandOutput>;
/**
 * deserializeAws_restJson1PutConfigurationSetDeliveryOptionsCommand
 */
export declare const de_PutConfigurationSetDeliveryOptionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutConfigurationSetDeliveryOptionsCommandOutput>;
/**
 * deserializeAws_restJson1PutConfigurationSetReputationOptionsCommand
 */
export declare const de_PutConfigurationSetReputationOptionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutConfigurationSetReputationOptionsCommandOutput>;
/**
 * deserializeAws_restJson1PutConfigurationSetSendingOptionsCommand
 */
export declare const de_PutConfigurationSetSendingOptionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutConfigurationSetSendingOptionsCommandOutput>;
/**
 * deserializeAws_restJson1PutConfigurationSetSuppressionOptionsCommand
 */
export declare const de_PutConfigurationSetSuppressionOptionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutConfigurationSetSuppressionOptionsCommandOutput>;
/**
 * deserializeAws_restJson1PutConfigurationSetTrackingOptionsCommand
 */
export declare const de_PutConfigurationSetTrackingOptionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutConfigurationSetTrackingOptionsCommandOutput>;
/**
 * deserializeAws_restJson1PutConfigurationSetVdmOptionsCommand
 */
export declare const de_PutConfigurationSetVdmOptionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutConfigurationSetVdmOptionsCommandOutput>;
/**
 * deserializeAws_restJson1PutDedicatedIpInPoolCommand
 */
export declare const de_PutDedicatedIpInPoolCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutDedicatedIpInPoolCommandOutput>;
/**
 * deserializeAws_restJson1PutDedicatedIpPoolScalingAttributesCommand
 */
export declare const de_PutDedicatedIpPoolScalingAttributesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutDedicatedIpPoolScalingAttributesCommandOutput>;
/**
 * deserializeAws_restJson1PutDedicatedIpWarmupAttributesCommand
 */
export declare const de_PutDedicatedIpWarmupAttributesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutDedicatedIpWarmupAttributesCommandOutput>;
/**
 * deserializeAws_restJson1PutDeliverabilityDashboardOptionCommand
 */
export declare const de_PutDeliverabilityDashboardOptionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutDeliverabilityDashboardOptionCommandOutput>;
/**
 * deserializeAws_restJson1PutEmailIdentityConfigurationSetAttributesCommand
 */
export declare const de_PutEmailIdentityConfigurationSetAttributesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutEmailIdentityConfigurationSetAttributesCommandOutput>;
/**
 * deserializeAws_restJson1PutEmailIdentityDkimAttributesCommand
 */
export declare const de_PutEmailIdentityDkimAttributesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutEmailIdentityDkimAttributesCommandOutput>;
/**
 * deserializeAws_restJson1PutEmailIdentityDkimSigningAttributesCommand
 */
export declare const de_PutEmailIdentityDkimSigningAttributesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutEmailIdentityDkimSigningAttributesCommandOutput>;
/**
 * deserializeAws_restJson1PutEmailIdentityFeedbackAttributesCommand
 */
export declare const de_PutEmailIdentityFeedbackAttributesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutEmailIdentityFeedbackAttributesCommandOutput>;
/**
 * deserializeAws_restJson1PutEmailIdentityMailFromAttributesCommand
 */
export declare const de_PutEmailIdentityMailFromAttributesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutEmailIdentityMailFromAttributesCommandOutput>;
/**
 * deserializeAws_restJson1PutSuppressedDestinationCommand
 */
export declare const de_PutSuppressedDestinationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutSuppressedDestinationCommandOutput>;
/**
 * deserializeAws_restJson1SendBulkEmailCommand
 */
export declare const de_SendBulkEmailCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<SendBulkEmailCommandOutput>;
/**
 * deserializeAws_restJson1SendCustomVerificationEmailCommand
 */
export declare const de_SendCustomVerificationEmailCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<SendCustomVerificationEmailCommandOutput>;
/**
 * deserializeAws_restJson1SendEmailCommand
 */
export declare const de_SendEmailCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<SendEmailCommandOutput>;
/**
 * deserializeAws_restJson1TagResourceCommand
 */
export declare const de_TagResourceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<TagResourceCommandOutput>;
/**
 * deserializeAws_restJson1TestRenderEmailTemplateCommand
 */
export declare const de_TestRenderEmailTemplateCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<TestRenderEmailTemplateCommandOutput>;
/**
 * deserializeAws_restJson1UntagResourceCommand
 */
export declare const de_UntagResourceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UntagResourceCommandOutput>;
/**
 * deserializeAws_restJson1UpdateConfigurationSetEventDestinationCommand
 */
export declare const de_UpdateConfigurationSetEventDestinationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateConfigurationSetEventDestinationCommandOutput>;
/**
 * deserializeAws_restJson1UpdateContactCommand
 */
export declare const de_UpdateContactCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateContactCommandOutput>;
/**
 * deserializeAws_restJson1UpdateContactListCommand
 */
export declare const de_UpdateContactListCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateContactListCommandOutput>;
/**
 * deserializeAws_restJson1UpdateCustomVerificationEmailTemplateCommand
 */
export declare const de_UpdateCustomVerificationEmailTemplateCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateCustomVerificationEmailTemplateCommandOutput>;
/**
 * deserializeAws_restJson1UpdateEmailIdentityPolicyCommand
 */
export declare const de_UpdateEmailIdentityPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateEmailIdentityPolicyCommandOutput>;
/**
 * deserializeAws_restJson1UpdateEmailTemplateCommand
 */
export declare const de_UpdateEmailTemplateCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateEmailTemplateCommandOutput>;
/**
 * deserializeAws_restJson1UpdateReputationEntityCustomerManagedStatusCommand
 */
export declare const de_UpdateReputationEntityCustomerManagedStatusCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateReputationEntityCustomerManagedStatusCommandOutput>;
/**
 * deserializeAws_restJson1UpdateReputationEntityPolicyCommand
 */
export declare const de_UpdateReputationEntityPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateReputationEntityPolicyCommandOutput>;
