import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutConfigurationSetSuppressionOptionsRequest,
  PutConfigurationSetSuppressionOptionsResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutConfigurationSetSuppressionOptionsCommandInput
  extends PutConfigurationSetSuppressionOptionsRequest {}
export interface PutConfigurationSetSuppressionOptionsCommandOutput
  extends PutConfigurationSetSuppressionOptionsResponse,
    __MetadataBearer {}
declare const PutConfigurationSetSuppressionOptionsCommand_base: {
  new (
    input: PutConfigurationSetSuppressionOptionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutConfigurationSetSuppressionOptionsCommandInput,
    PutConfigurationSetSuppressionOptionsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutConfigurationSetSuppressionOptionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutConfigurationSetSuppressionOptionsCommandInput,
    PutConfigurationSetSuppressionOptionsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutConfigurationSetSuppressionOptionsCommand extends PutConfigurationSetSuppressionOptionsCommand_base {
  protected static __types: {
    api: {
      input: PutConfigurationSetSuppressionOptionsRequest;
      output: {};
    };
    sdk: {
      input: PutConfigurationSetSuppressionOptionsCommandInput;
      output: PutConfigurationSetSuppressionOptionsCommandOutput;
    };
  };
}
