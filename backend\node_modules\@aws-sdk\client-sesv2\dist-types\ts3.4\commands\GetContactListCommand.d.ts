import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  GetContactListRequest,
  GetContactListResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetContactListCommandInput extends GetContactListRequest {}
export interface GetContactListCommandOutput
  extends GetContactListResponse,
    __MetadataBearer {}
declare const GetContactListCommand_base: {
  new (
    input: GetContactListCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetContactListCommandInput,
    GetContactListCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetContactListCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetContactListCommandInput,
    GetContactListCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetContactListCommand extends GetContactListCommand_base {
  protected static __types: {
    api: {
      input: GetContactListRequest;
      output: GetContactListResponse;
    };
    sdk: {
      input: GetContactListCommandInput;
      output: GetContactListCommandOutput;
    };
  };
}
