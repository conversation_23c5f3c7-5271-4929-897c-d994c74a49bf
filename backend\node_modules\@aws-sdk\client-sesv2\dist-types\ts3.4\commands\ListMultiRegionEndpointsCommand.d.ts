import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  ListMultiRegionEndpointsRequest,
  ListMultiRegionEndpointsResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface ListMultiRegionEndpointsCommandInput
  extends ListMultiRegionEndpointsRequest {}
export interface ListMultiRegionEndpointsCommandOutput
  extends ListMultiRegionEndpointsResponse,
    __MetadataBearer {}
declare const ListMultiRegionEndpointsCommand_base: {
  new (
    input: ListMultiRegionEndpointsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListMultiRegionEndpointsCommandInput,
    ListMultiRegionEndpointsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListMultiRegionEndpointsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListMultiRegionEndpointsCommandInput,
    ListMultiRegionEndpointsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListMultiRegionEndpointsCommand extends ListMultiRegionEndpointsCommand_base {
  protected static __types: {
    api: {
      input: ListMultiRegionEndpointsRequest;
      output: ListMultiRegionEndpointsResponse;
    };
    sdk: {
      input: ListMultiRegionEndpointsCommandInput;
      output: ListMultiRegionEndpointsCommandOutput;
    };
  };
}
