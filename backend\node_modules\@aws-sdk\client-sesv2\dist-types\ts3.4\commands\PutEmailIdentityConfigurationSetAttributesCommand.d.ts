import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutEmailIdentityConfigurationSetAttributesRequest,
  PutEmailIdentityConfigurationSetAttributesResponse,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface PutEmailIdentityConfigurationSetAttributesCommandInput
  extends PutEmailIdentityConfigurationSetAttributesRequest {}
export interface PutEmailIdentityConfigurationSetAttributesCommandOutput
  extends PutEmailIdentityConfigurationSetAttributesResponse,
    __MetadataBearer {}
declare const PutEmailIdentityConfigurationSetAttributesCommand_base: {
  new (
    input: PutEmailIdentityConfigurationSetAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutEmailIdentityConfigurationSetAttributesCommandInput,
    PutEmailIdentityConfigurationSetAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutEmailIdentityConfigurationSetAttributesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutEmailIdentityConfigurationSetAttributesCommandInput,
    PutEmailIdentityConfigurationSetAttributesCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutEmailIdentityConfigurationSetAttributesCommand extends PutEmailIdentityConfigurationSetAttributesCommand_base {
  protected static __types: {
    api: {
      input: PutEmailIdentityConfigurationSetAttributesRequest;
      output: {};
    };
    sdk: {
      input: PutEmailIdentityConfigurationSetAttributesCommandInput;
      output: PutEmailIdentityConfigurationSetAttributesCommandOutput;
    };
  };
}
