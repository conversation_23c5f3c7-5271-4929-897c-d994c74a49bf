import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetDedicatedIpsRequest,
  GetDedicatedIpsResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESv2ClientResolvedConfig,
} from "../SESv2Client";
export { __MetadataBearer };
export { $Command };
export interface GetDedicatedIpsCommandInput extends GetDedicatedIpsRequest {}
export interface GetDedicatedIpsCommandOutput
  extends GetDedicatedIpsResponse,
    __MetadataBearer {}
declare const GetDedicatedIpsCommand_base: {
  new (
    input: GetDedicatedIpsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetDedicatedIpsCommandInput,
    GetDedicatedIpsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [GetDedicatedIpsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    GetDedicatedIpsCommandInput,
    GetDedicatedIpsCommandOutput,
    SESv2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetDedicatedIpsCommand extends GetDedicatedIpsCommand_base {
  protected static __types: {
    api: {
      input: GetDedicatedIpsRequest;
      output: GetDedicatedIpsResponse;
    };
    sdk: {
      input: GetDedicatedIpsCommandInput;
      output: GetDedicatedIpsCommandOutput;
    };
  };
}
