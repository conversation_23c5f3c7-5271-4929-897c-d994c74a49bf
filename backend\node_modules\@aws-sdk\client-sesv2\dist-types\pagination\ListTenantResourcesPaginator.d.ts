import { Paginator } from "@smithy/types";
import { ListTenantResourcesCommandInput, ListTenantResourcesCommandOutput } from "../commands/ListTenantResourcesCommand";
import { SESv2PaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListTenantResources: (config: SESv2PaginationConfiguration, input: ListTenantResourcesCommandInput, ...rest: any[]) => Paginator<ListTenantResourcesCommandOutput>;
