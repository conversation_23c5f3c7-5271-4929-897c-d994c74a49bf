import { Paginator } from "@smithy/types";
import { ListDedicatedIpPoolsCommandInput, ListDedicatedIpPoolsCommandOutput } from "../commands/ListDedicatedIpPoolsCommand";
import { SESv2PaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListDedicatedIpPools: (config: SESv2PaginationConfiguration, input: ListDedicatedIpPoolsCommandInput, ...rest: any[]) => Paginator<ListDedicatedIpPoolsCommandOutput>;
